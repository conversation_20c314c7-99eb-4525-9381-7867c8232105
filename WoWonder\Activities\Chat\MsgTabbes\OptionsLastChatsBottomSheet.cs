﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Android.Content;
using Android.OS;
using Android.Text;
using Android.Views;
using Android.Widget;
using AndroidHUD;
using AndroidX.RecyclerView.Widget;
using Google.Android.Material.BottomSheet;
using Google.Android.Material.Dialog;
using Newtonsoft.Json;
using WoWonder.Activities.Chat.GroupChat;
using WoWonder.Adapters;
using WoWonder.Helpers.Controller;
using WoWonder.Helpers.Model;
using WoWonder.Helpers.Utils;
using WoWonder.SQLite;
using WoWonderClient.Classes.GroupChat;
using WoWonderClient.Classes.Message;
using WoWonderClient.Requests;
using Exception = System.Exception;

namespace WoWonder.Activities.Chat.MsgTabbes
{
    public class OptionsLastChatsBottomSheet : BottomSheetDialogFragment, IDialogInputCallBack
    {
        #region Variables Basic

        private TextView TitleText;
        private ImageView IconClose;

        private ChatTabbedMainActivity GlobalContext;
        //wael  add Mute call
        private RecyclerView MRecycler;
        private LinearLayoutManager LayoutManager;
        private ItemOptionAdapter MAdapter;

        private string Type, Page;
        private ChatObject DataChatObject;


        #endregion

        #region General

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            // Create your fragment here
            GlobalContext = ChatTabbedMainActivity.GetInstance();
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            try
            {
                Context contextThemeWrapper = WoWonderTools.IsTabDark() ? new ContextThemeWrapper(Activity, Resource.Style.MyTheme_Dark) : new ContextThemeWrapper(Activity, Resource.Style.MyTheme);
                // clone the inflater using the ContextThemeWrapper
                LayoutInflater localInflater = inflater.CloneInContext(contextThemeWrapper);

                View view = localInflater?.Inflate(Resource.Layout.BottomSheetDefaultLayout, container, false);
                return view;
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
                return null!;
            }
        }

        public override void OnViewCreated(View view, Bundle savedInstanceState)
        {
            try
            {
                base.OnViewCreated(view, savedInstanceState);
                InitComponent(view);
                SetRecyclerViewAdapters(view);

                LoadDataChat();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        public override void OnLowMemory()
        {
            try
            {
                GC.Collect(GC.MaxGeneration);
                base.OnLowMemory();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        #endregion

        #region Functions

        private void InitComponent(View view)
        {
            try
            {
                IconClose = view.FindViewById<ImageView>(Resource.Id.iconClose);
                IconClose.Click += IconCloseOnClick;

                TitleText = view.FindViewById<TextView>(Resource.Id.titleText);
                TitleText.Text = " ";
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }
        private void SetRecyclerViewAdapters(View view)
        {
            try
            {
                MRecycler = (RecyclerView)view.FindViewById(Resource.Id.recyler);

                MAdapter = new ItemOptionAdapter(Activity)
                {
                    ItemOptionList = new ObservableCollection<Classes.ItemOptionObject>()
                };
                MAdapter.ItemClick += MAdapterOnItemClick;
                LayoutManager = new LinearLayoutManager(Context);
                MRecycler.SetLayoutManager(LayoutManager);
                MRecycler.SetAdapter(MAdapter);
                MRecycler.HasFixedSize = true;
                MRecycler.SetItemViewCacheSize(50);
                MRecycler.GetLayoutManager().ItemPrefetchEnabled = true;
                MRecycler.GetRecycledViewPool().Clear();
                MRecycler.SetAdapter(MAdapter);
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        #endregion

        #region Event

        private void IconCloseOnClick(object sender, EventArgs e)
        {
            Dismiss();
        }

        private void MAdapterOnItemClick(object sender, ItemOptionAdapterClickEventArgs e)
        {
            try
            {
                var position = e.Position;
                if (position > -1)
                {
                    var item = MAdapter.GetItem(position);
                    if (item?.Id == "1") //Archive
                    {
                        ArchiveLayoutOnClick();
                    }
                    else if (item?.Id == "2") //DeleteMessage
                    {
                        DeleteLayoutOnClick();
                    }
                    else if (item?.Id == "3") //Pin
                    {
                        PinLayoutOnClick();
                    }
                    else if (item?.Id == "4") //MuteNotification
                    {
                        MuteLayoutOnClick();
                    }
                    else if (item?.Id == "5") //MarkAsRead
                    {
                        ReadLayoutOnClick();
                    }
                    else if (item?.Id == "6") //Block
                    {
                        BlockLayoutOnClick();
                    }
                    else if (item?.Id == "7") //View Profile
                    {
                        ProfileLayoutOnClick();
                    }
                    else if (item?.Id == "8") //View Profile
                    {
                        ReportUserOnClick(item.Text);
                    }
                    else if (item?.Id == "9") //GroupInfo
                    {
                        GroupInfoLayoutOnClick();
                    }
                    else if (item?.Id == "10") //ExitGroup
                    {
                        ExitGroupLayoutOnClick();
                    }
                    else if (item?.Id == "11") //AddMembers
                    {
                        AddMembersLayoutOnClick();
                    }
                }
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }


        //Add Members to group
        private void AddMembersLayoutOnClick()
        {
            try
            {
                Intent intent = new Intent(Activity, typeof(EditGroupChatActivity));
                intent.PutExtra("GroupObject", JsonConvert.SerializeObject(DataChatObject));
                intent.PutExtra("Type", "Edit");
                Activity.StartActivity(intent);

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Exit Group
        private void ExitGroupLayoutOnClick()
        {
            try
            {
                if (!Methods.CheckConnectivity())
                {
                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_CheckYourInternetConnection), ToastLength.Short);
                }
                else
                {
                    var dialog = new MaterialAlertDialogBuilder(Activity);
                    dialog.SetMessage(GetText(Resource.String.Lbl_AreYouSureExitGroup));
                    dialog.SetPositiveButton(GetText(Resource.String.Lbl_Exit), async (materialDialog, action) =>
                    {
                        try
                        {
                            //Show a progress
                            AndHUD.Shared.Show(Activity, GetText(Resource.String.Lbl_Loading));

                            var (apiStatus, respond) = await RequestsAsync.GroupChat.ExitGroupChatAsync(DataChatObject.GroupId);
                            if (apiStatus == 200)
                            {
                                if (respond is AddOrRemoveUserToGroupObject result)
                                {
                                    Console.WriteLine(result.MessageData);

                                    ToastUtils.ShowToast(Activity, Activity.GetString(Resource.String.Lbl_GroupSuccessfullyLeaved), ToastLength.Short);

                                    //remove item to my Group list  
                                    var adapter = GlobalContext?.ChatTab?.LastGroupChatsTab.MAdapter;
                                    var data = adapter?.LastChatsList?.FirstOrDefault(a => a.LastChat?.GroupId == DataChatObject.GroupId);
                                    if (data != null)
                                    {
                                        adapter.LastChatsList.Remove(data);
                                        adapter.NotifyItemRemoved(adapter.LastChatsList.IndexOf(data));
                                    }

                                    AndHUD.Shared.ShowSuccess(Activity);
                                }
                            }
                            else Methods.DisplayReportResult(Activity, respond);

                            AndHUD.Shared.Dismiss();

                            Dismiss();
                        }
                        catch (Exception e)
                        {
                            Methods.DisplayReportResultTrack(e);
                        }
                    });
                    dialog.SetNegativeButton(GetText(Resource.String.Lbl_Cancel), new MaterialDialogUtils());

                    dialog.Show();
                }
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Group Info (Profile)  
        private void GroupInfoLayoutOnClick()
        {
            try
            {
                Intent intent = new Intent(Activity, typeof(EditGroupChatActivity));
                intent.PutExtra("GroupObject", JsonConvert.SerializeObject(DataChatObject));
                intent.PutExtra("Type", "Profile");
                Activity.StartActivity(intent);

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //View Profile
        private void ProfileLayoutOnClick()
        {
            try
            {
                WoWonderTools.OpenProfile(Activity, DataChatObject.UserId, DataChatObject.UserData);

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Report User
        private void ReportUserOnClick(string text)
        {
            try
            {
                string userId = DataChatObject.UserId;

                if (text == GetText(Resource.String.Lbl_ReportThisUser))
                {
                    var dialog = new MaterialAlertDialogBuilder(Context);
                    dialog.SetTitle(GetString(Resource.String.Lbl_ReportThisUser));

                    EditText input = new EditText(Context);
                    input.SetHint(Resource.String.text);
                    input.InputType = InputTypes.TextFlagImeMultiLine;
                    LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MatchParent, ViewGroup.LayoutParams.WrapContent);
                    input.LayoutParameters = lp;

                    dialog.SetView(input);

                    dialog.SetPositiveButton(GetText(Resource.String.Lbl_Update), new MaterialDialogUtils(input, this));
                    dialog.SetNegativeButton(GetText(Resource.String.Lbl_Cancel), new MaterialDialogUtils());

                    dialog.Show();
                }
                else if (text == GetText(Resource.String.Lbl_CancelReport))
                {
                    if (!Methods.CheckConnectivity())
                        ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_CheckYourInternetConnection), ToastLength.Short);
                    else
                    {
                        DataChatObject.IsReported = false;

                        var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                        var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                        if (checkUser != null)
                        {
                            var index = mAdapter.LastChatsList.IndexOf(checkUser);

                            checkUser.LastChat.IsReported = false;

                            mAdapter.NotifyItemChanged(index);
                        }

                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Global.ReportUserAsync(userId, "") });
                    }
                }

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Block User
        private async void BlockLayoutOnClick()
        {
            try
            {
                string userId = DataChatObject.UserId;

                if (Methods.CheckConnectivity())
                {
                    if (!DataChatObject.IsBlocked)
                    {
                        DataChatObject.IsBlocked = true;
                        var (apiStatus, respond) = await RequestsAsync.Global.BlockUserAsync(userId, true); //true >> "block" 
                        if (apiStatus == 200)
                        {
                            var dbDatabase = new SqLiteDatabase();
                            //dbDatabase.Insert_Or_Replace_OR_Delete_UsersContact(DataUserChat, "Delete"); 
                            dbDatabase.DeleteAllMessagesUser(UserDetails.UserId, userId);

                            Methods.Path.DeleteAll_FolderUser(userId);

                            ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_Blocked_successfully), ToastLength.Short);

                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                            var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                            if (checkUser != null)
                            {
                                checkUser.LastChat.IsBlocked = true;
                            }


                            DeleteLayoutOnClick();
                        }
                        else
                            Methods.DisplayReportResultTrack(respond);
                    }
                    else
                    {
                        DataChatObject.IsBlocked = false;

                        var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                        var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                        if (checkUser != null)
                        {
                            var index = mAdapter.LastChatsList.IndexOf(checkUser);

                            checkUser.LastChat.IsBlocked = false;

                            mAdapter.NotifyItemChanged(index);
                        }

                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Global.BlockUserAsync(DataChatObject.UserId, false) });//false >> "un-block"
                        ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_Unblock_successfully), ToastLength.Short);
                    }
                }
                else
                {
                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_CheckYourInternetConnection), ToastLength.Short);
                }

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Mark As Read/UnRead  
        //if (Seen == "0") //not read Change to read (Normal) >> Seen = "1"; 
        //else //read Change to unread (Bold) >> Seen = "0";
        private void ReadLayoutOnClick()
        {
            try
            {
                if (Page == "Archived")
                {
                    var mAdapter = GlobalContext?.ChatTab?.ArchivedChatsTab?.MAdapter;
                    var seen = DataChatObject.LastMessage.LastMessageClass.Seen == "0" ? DataChatObject.LastMessage.LastMessageClass.Seen = "1" : DataChatObject.LastMessage.LastMessageClass.Seen = "0";

                    var checkUser = mAdapter?.LastChatsList?.FirstOrDefault(a => a.LastChat?.ChatId == DataChatObject.ChatId);
                    if (checkUser != null)
                    {
                        checkUser.LastChat.LastMessage.LastMessageClass.Seen = seen;
                        mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkUser), "WithoutBlobRead");
                    }
                }

                switch (Type)
                {
                    case "user":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                            var seen = DataChatObject.LastMessage.LastMessageClass.Seen == "0" ? DataChatObject.LastMessage.LastMessageClass.Seen = "1" : DataChatObject.LastMessage.LastMessageClass.Seen = "0";

                            var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                            if (checkUser != null)
                            {
                                checkUser.LastChat.LastMessage.LastMessageClass.Seen = seen;
                                mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkUser), "WithoutBlobRead");
                            }
                            break;
                        }
                    case "page":
                        {
                            Classes.LastChatsClass checkUser = null!;

                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                            var seen = DataChatObject.LastMessage.LastMessageClass.Seen == "0" ? DataChatObject.LastMessage.LastMessageClass.Seen = "1" : DataChatObject.LastMessage.LastMessageClass.Seen = "0";

                            var checkPage = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId && a.LastChat?.LastMessage.LastMessageClass?.ToData?.UserId == DataChatObject.LastMessage.LastMessageClass?.ToData?.UserId);
                            if (checkPage != null)
                            {
                                var userAdminPage = DataChatObject.UserId;
                                if (userAdminPage == DataChatObject.LastMessage.LastMessageClass.ToData.UserId)
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.UserData?.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.UserData?.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.UserData?.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);
                                }
                                else
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.ToData.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.ToData.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.ToData.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);
                                }
                            }
                            else
                                checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId);

                            if (checkUser != null)
                            {
                                checkUser.LastChat.LastMessage.LastMessageClass.Seen = seen;
                                mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkUser), "WithoutBlobRead");
                            }
                            break;
                        }
                    case "group":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastGroupChatsTab?.MAdapter;
                            var seen = DataChatObject.LastMessage.LastMessageClass.Seen == "0" ? DataChatObject.LastMessage.LastMessageClass.Seen = "1" : DataChatObject.LastMessage.LastMessageClass.Seen = "0";

                            var checkGroup = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.GroupId == DataChatObject.GroupId);
                            if (checkGroup?.LastChat != null)
                            {
                                checkGroup.LastChat.LastMessage.LastMessageClass.Seen = seen;
                                mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkGroup), "WithoutBlobRead");
                            }
                            break;
                        }
                }

                if (Methods.CheckConnectivity())
                    PollyController.RunRetryPolicyFunction(new List<Func<Task>> { RequestsAsync.Message.ReadChatsAsync });

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Mark As Mute/UnMute
        private void MuteLayoutOnClick()
        {
            try
            {
                bool isMute = !DataChatObject.IsMute;
                Classes.OptionLastChat muteObject = null!;

                if (Page == "Archived")
                {
                    var mAdapter = GlobalContext?.ChatTab?.ArchivedChatsTab?.MAdapter;

                    var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.ChatId == DataChatObject.ChatId);
                    if (checkUser != null)
                    {
                        checkUser.LastChat.IsMute = isMute;
                        checkUser.LastChat.Mute.Notify = isMute ? "no" : "yes";

                        mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkUser), "WithoutBlobMute");
                    }
                }

                switch (Type)
                {
                    case "user":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;

                            var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                            if (checkUser != null)
                            {
                                checkUser.LastChat.IsMute = isMute;

                                checkUser.LastChat.Mute.Notify = isMute ? "no" : "yes";

                                mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkUser), "WithoutBlobMute");
                                muteObject = new Classes.OptionLastChat
                                {
                                    ChatType = "user",
                                    ChatId = DataChatObject.ChatId,
                                    UserId = DataChatObject.UserId,
                                    GroupId = "",
                                    PageId = "",
                                    Name = DataChatObject.Name
                                };
                            }
                            break;
                        }
                    case "page":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;

                            Classes.LastChatsClass checkUser = null!;

                            var checkPage = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId && a.LastChat?.LastMessage.LastMessageClass?.ToData?.UserId == DataChatObject.LastMessage.LastMessageClass?.ToData?.UserId);
                            if (checkPage != null)
                            {
                                var userAdminPage = DataChatObject.UserId;
                                if (userAdminPage == DataChatObject.LastMessage.LastMessageClass.ToData.UserId)
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.UserData?.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.UserData?.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.UserData?.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);

                                    muteObject = new Classes.OptionLastChat
                                    {
                                        ChatType = "page",
                                        ChatId = DataChatObject.ChatId,
                                        UserId = userId,
                                        GroupId = "",
                                        PageId = DataChatObject.PageId,
                                        Name = name
                                    };
                                }
                                else
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.ToData.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.ToData.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.ToData.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);

                                    muteObject = new Classes.OptionLastChat
                                    {
                                        ChatType = "page",
                                        ChatId = DataChatObject.ChatId,
                                        UserId = userId,
                                        GroupId = "",
                                        PageId = DataChatObject.PageId,
                                        Name = name
                                    };
                                }
                            }
                            else
                            {
                                checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId);
                                muteObject = new Classes.OptionLastChat
                                {
                                    ChatType = "page",
                                    ChatId = DataChatObject.ChatId,
                                    UserId = "",
                                    GroupId = "",
                                    PageId = DataChatObject.PageId,
                                    Name = DataChatObject.PageName
                                };
                            }

                            if (checkUser != null)
                            {
                                checkUser.LastChat.IsMute = isMute;
                                checkUser.LastChat.Mute.Notify = isMute ? "no" : "yes";

                                mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkUser), "WithoutBlobMute");
                            }
                            break;
                        }
                    case "group":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastGroupChatsTab?.MAdapter;
                            var checkGroup = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.GroupId == DataChatObject.GroupId);
                            if (checkGroup != null)
                            {
                                checkGroup.LastChat.IsMute = isMute;

                                checkGroup.LastChat.Mute.Notify = isMute ? "no" : "yes";

                                mAdapter?.NotifyItemChanged(mAdapter.LastChatsList.IndexOf(checkGroup), "WithoutBlobMute");

                                muteObject = new Classes.OptionLastChat
                                {
                                    ChatType = "group",
                                    ChatId = DataChatObject.ChatId,
                                    UserId = "",
                                    GroupId = DataChatObject.GroupId,
                                    PageId = "",
                                    Name = DataChatObject.GroupName
                                };
                            }
                            break;
                        }
                }

                if (isMute)
                {
                    if (muteObject != null)
                    {
                        ListUtils.MuteList.Add(muteObject);

                        var sqLiteDatabase = new SqLiteDatabase();
                        sqLiteDatabase.InsertORDelete_Mute(muteObject);
                    }

                    var dictionary = new Dictionary<string, string>
                    {
                        {"notify", "no"},
                    };

                    if (Methods.CheckConnectivity())
                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.MuteChatsInfoAsync(DataChatObject.ChatId, Type, dictionary) });

                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_AddedMute), ToastLength.Long);
                }
                else
                {
                    var checkMute = ListUtils.MuteList.FirstOrDefault(a => muteObject != null && a.ChatId == muteObject.ChatId && a.ChatType == muteObject.ChatType);
                    if (checkMute != null)
                    {
                        ListUtils.MuteList.Remove(checkMute);

                        var sqLiteDatabase = new SqLiteDatabase();
                        sqLiteDatabase.InsertORDelete_Mute(checkMute);
                    }

                    var dictionary = new Dictionary<string, string>
                    {
                        {"notify", "yes"},
                    };

                    //if (globalMute != null)
                    //{
                    //    dictionary.Add("call_chat", globalMute.CallChat);
                    //    dictionary.Add("archive", globalMute.Archive);
                    //    dictionary.Add("pin", globalMute.Pin);
                    //}

                    if (Methods.CheckConnectivity())
                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.MuteChatsInfoAsync(DataChatObject.ChatId, Type, dictionary) });

                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_RemovedMute), ToastLength.Long);
                }

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Mark Pin
        private void PinLayoutOnClick()
        {
            try
            {
                bool isPin = !DataChatObject.IsPin;
                Classes.OptionLastChat pinObject = null!;

                if (Page == "Archived")
                {
                    var mAdapter = GlobalContext?.ChatTab?.ArchivedChatsTab?.MAdapter;

                    var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.ChatId == DataChatObject.ChatId);
                    if (checkUser != null)
                    {
                        var index = mAdapter.LastChatsList.IndexOf(checkUser);
                        checkUser.LastChat.IsPin = isPin;

                        checkUser.LastChat.Mute.Pin = isPin ? "yes" : "no";

                        if (isPin)
                        {
                            var checkPin = mAdapter.LastChatsList.LastOrDefault(o => o.LastChat != null && o.LastChat.IsPin);
                            if (checkPin != null)
                            {
                                var toIndex = mAdapter.LastChatsList.IndexOf(checkPin) + 1;

                                if (ListUtils.FriendRequestsList.Count > 0)
                                    toIndex++;

                                if (mAdapter.LastChatsList.Count > toIndex)
                                {
                                    mAdapter.LastChatsList.Move(index, toIndex);
                                    mAdapter.NotifyItemMoved(index, toIndex);
                                }

                                mAdapter.NotifyItemChanged(toIndex, "WithoutBlobPin");
                            }
                            else
                            {
                                if (ListUtils.FriendRequestsList.Count > 0)
                                {
                                    mAdapter.LastChatsList.Move(index, 1);
                                    mAdapter.NotifyItemMoved(index, 1);
                                    mAdapter.NotifyItemChanged(1, "WithoutBlobPin");
                                }
                                else
                                {
                                    mAdapter.LastChatsList.Move(index, 0);
                                    mAdapter.NotifyItemMoved(index, 0);
                                    mAdapter.NotifyItemChanged(0, "WithoutBlobPin");
                                }
                            }
                        }
                        else
                        {
                            mAdapter.NotifyItemChanged(index, "WithoutBlobPin");
                        }
                    }
                }

                switch (Type)
                {
                    case "user":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;

                            var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                            if (checkUser != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(checkUser);
                                checkUser.LastChat.IsPin = isPin;

                                checkUser.LastChat.Mute.Pin = isPin ? "yes" : "no";

                                if (isPin)
                                {
                                    var checkPin = mAdapter.LastChatsList.LastOrDefault(o => o.LastChat != null && o.LastChat.IsPin);
                                    if (checkPin != null)
                                    {
                                        var toIndex = mAdapter.LastChatsList.IndexOf(checkPin) + 1;

                                        if (ListUtils.FriendRequestsList.Count > 0)
                                            toIndex++;

                                        if (mAdapter.LastChatsList.Count > toIndex)
                                        {
                                            mAdapter.LastChatsList.Move(index, toIndex);
                                            mAdapter.NotifyItemMoved(index, toIndex);
                                        }

                                        mAdapter.NotifyItemChanged(toIndex, "WithoutBlobPin");
                                    }
                                    else
                                    {
                                        if (ListUtils.FriendRequestsList.Count > 0)
                                        {
                                            mAdapter.LastChatsList.Move(index, 1);
                                            mAdapter.NotifyItemMoved(index, 1);
                                            mAdapter.NotifyItemChanged(1, "WithoutBlobPin");
                                        }
                                        else
                                        {
                                            mAdapter.LastChatsList.Move(index, 0);
                                            mAdapter.NotifyItemMoved(index, 0);
                                            mAdapter.NotifyItemChanged(0, "WithoutBlobPin");
                                        }
                                    }
                                }
                                else
                                {
                                    mAdapter.NotifyItemChanged(index, "WithoutBlobPin");
                                }

                                pinObject = new Classes.OptionLastChat
                                {
                                    ChatType = "user",
                                    ChatId = DataChatObject.ChatId,
                                    UserId = DataChatObject.UserId,
                                    GroupId = "",
                                    PageId = "",
                                    Name = DataChatObject.Name
                                };
                            }
                            break;
                        }
                    case "page":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;

                            Classes.LastChatsClass checkUser = null!;

                            var checkPage = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId && a.LastChat?.LastMessage.LastMessageClass?.ToData?.UserId == DataChatObject.LastMessage.LastMessageClass?.ToData?.UserId);
                            if (checkPage != null)
                            {
                                var userAdminPage = DataChatObject.UserId;
                                if (userAdminPage == DataChatObject.LastMessage.LastMessageClass.ToData.UserId)
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.UserData?.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.UserData?.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.UserData?.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);

                                    pinObject = new Classes.OptionLastChat
                                    {
                                        ChatType = "page",
                                        ChatId = DataChatObject.ChatId,
                                        UserId = userId,
                                        GroupId = "",
                                        PageId = DataChatObject.PageId,
                                        Name = name
                                    };
                                }
                                else
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.ToData.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.ToData.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.ToData.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);

                                    pinObject = new Classes.OptionLastChat
                                    {
                                        ChatType = "page",
                                        ChatId = DataChatObject.ChatId,
                                        UserId = userId,
                                        GroupId = "",
                                        PageId = DataChatObject.PageId,
                                        Name = name
                                    };
                                }
                            }
                            else
                            {
                                checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId);
                                pinObject = new Classes.OptionLastChat
                                {
                                    ChatType = "page",
                                    ChatId = DataChatObject.ChatId,
                                    UserId = "",
                                    GroupId = "",
                                    PageId = DataChatObject.PageId,
                                    Name = DataChatObject.PageName
                                };
                            }

                            if (checkUser != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(checkUser);
                                checkUser.LastChat.IsPin = isPin;
                                checkUser.LastChat.Mute.Pin = isPin ? "yes" : "no";

                                if (isPin)
                                {
                                    if (ListUtils.FriendRequestsList.Count > 0)
                                    {
                                        mAdapter.LastChatsList.Move(index, 1);
                                        mAdapter.NotifyItemMoved(index, 1);
                                        mAdapter.NotifyItemChanged(1, "WithoutBlobPin");
                                    }
                                    else
                                    {
                                        mAdapter.LastChatsList.Move(index, 0);
                                        mAdapter.NotifyItemMoved(index, 0);
                                        mAdapter.NotifyItemChanged(0, "WithoutBlobPin");
                                    }

                                    //var checkPin = mAdapter.LastChatsList.LastOrDefault(o => o.LastChat != null && o.LastChat.IsPin);
                                    //if (checkPin != null)
                                    //{
                                    //    var toIndex = mAdapter.LastChatsList.IndexOf(checkPin) + 1;

                                    //    mAdapter.LastChatsList.Move(index, toIndex);
                                    //    mAdapter.NotifyItemMoved(index, toIndex);
                                    //    mAdapter.NotifyItemChanged(toIndex);
                                    //}
                                    //else
                                    //{
                                    //    if (ListUtils.FriendRequestsList.Count > 0)
                                    //    {
                                    //        mAdapter.LastChatsList.Move(index, 1);
                                    //        mAdapter.NotifyItemMoved(index, 1);
                                    //        mAdapter.NotifyItemChanged(1);
                                    //    }
                                    //    else
                                    //    {
                                    //        mAdapter.LastChatsList.Move(index, 0);
                                    //        mAdapter.NotifyItemMoved(index, 0);
                                    //        mAdapter.NotifyItemChanged(0);
                                    //    }
                                    //}
                                }
                                else
                                {
                                    mAdapter.NotifyItemChanged(index, "WithoutBlobPin");
                                }
                            }
                            break;
                        }
                    //break;
                    case "group":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastGroupChatsTab?.MAdapter;

                            var checkGroup = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.GroupId == DataChatObject.GroupId);
                            if (checkGroup?.LastChat != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(checkGroup);

                                checkGroup.LastChat.IsPin = isPin;
                                checkGroup.LastChat.Mute.Pin = isPin ? "yes" : "no";

                                if (isPin)
                                {
                                    var checkPin = mAdapter.LastChatsList.LastOrDefault(o => o.LastChat != null && o.LastChat.IsPin);
                                    if (checkPin != null)
                                    {
                                        var toIndex = mAdapter.LastChatsList.IndexOf(checkPin) + 1;

                                        if (ListUtils.FriendRequestsList.Count > 0)
                                            toIndex++;

                                        if (mAdapter.LastChatsList.Count > toIndex)
                                        {
                                            mAdapter.LastChatsList.Move(index, toIndex);
                                            mAdapter.NotifyItemMoved(index, toIndex);
                                        }
                                        mAdapter.NotifyItemChanged(toIndex, "WithoutBlobPin");
                                    }
                                    else
                                    {
                                        if (ListUtils.FriendRequestsList.Count > 0)
                                        {
                                            mAdapter.LastChatsList.Move(index, 1);
                                            mAdapter.NotifyItemMoved(index, 1);
                                            mAdapter.NotifyItemChanged(1, "WithoutBlobPin");
                                        }
                                        else
                                        {
                                            mAdapter.LastChatsList.Move(index, 0);
                                            mAdapter.NotifyItemMoved(index, 0);
                                            mAdapter.NotifyItemChanged(0, "WithoutBlobPin");
                                        }
                                    }
                                }
                                else
                                {
                                    mAdapter.NotifyItemChanged(index, "WithoutBlobPin");
                                }

                                pinObject = new Classes.OptionLastChat
                                {
                                    ChatType = "group",
                                    ChatId = DataChatObject.ChatId,
                                    UserId = "",
                                    GroupId = DataChatObject.GroupId,
                                    PageId = "",
                                    Name = DataChatObject.GroupName
                                };
                            }
                            break;
                        }
                }

                if (isPin)
                {
                    if (pinObject != null)
                    {
                        ListUtils.PinList.Add(pinObject);

                        var sqLiteDatabase = new SqLiteDatabase();
                        sqLiteDatabase.InsertORDelete_Pin(pinObject);
                    }

                    var dictionary = new Dictionary<string, string>
                    {
                        {"pin", "yes"},
                    };

                    //if (globalMute != null)
                    //{
                    //    dictionary.Add("call_chat", globalMute.CallChat);
                    //    dictionary.Add("archive", globalMute.Archive);
                    //    dictionary.Add("notify", globalMute.Notify);
                    //}

                    if (Methods.CheckConnectivity())
                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.MuteChatsInfoAsync(DataChatObject.ChatId, Type, dictionary) });

                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_MessagePinned), ToastLength.Long);
                }
                else
                {
                    var checkPin = ListUtils.PinList.FirstOrDefault(a => pinObject != null && a.ChatId == pinObject.ChatId && a.ChatType == pinObject.ChatType);
                    if (checkPin != null)
                    {
                        ListUtils.PinList.Remove(checkPin);

                        var sqLiteDatabase = new SqLiteDatabase();
                        sqLiteDatabase.InsertORDelete_Pin(checkPin);
                    }

                    var dictionary = new Dictionary<string, string>
                    {
                        {"pin", "no"},
                    };

                    //if (globalMute != null)
                    //{
                    //    dictionary.Add("call_chat", globalMute.CallChat);
                    //    dictionary.Add("archive", globalMute.Archive);
                    //    dictionary.Add("notify", globalMute.Notify);
                    //}

                    if (Methods.CheckConnectivity())
                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.MuteChatsInfoAsync(DataChatObject.ChatId, Type, dictionary) });

                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_MessageUnPinned), ToastLength.Long);
                }

                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Delete Chat
        private void DeleteLayoutOnClick()
        {
            try
            {
                var dialog = new MaterialAlertDialogBuilder(Context);
                dialog.SetTitle(GetText(Resource.String.Lbl_DeleteTheEntireConversation));
                dialog.SetMessage(GetText(Resource.String.Lbl_OnceYouDeleteConversation));
                dialog.SetPositiveButton(GetText(Resource.String.Lbl_Yes), (materialDialog, action) =>
                {
                    try
                    {
                        if (!Methods.CheckConnectivity())
                        {
                            ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_CheckYourInternetConnection), ToastLength.Short);
                            return;
                        }

                        if (Page == "Archived")
                        {
                            var mAdapter = GlobalContext?.ChatTab?.ArchivedChatsTab?.MAdapter;
                            var chatToDelete = mAdapter?.LastChatsList?.FirstOrDefault(a => a.LastChat?.ChatId == DataChatObject.ChatId);
                            if (chatToDelete != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(chatToDelete);
                                if (index > -1)
                                {
                                    mAdapter?.LastChatsList?.Remove(chatToDelete);
                                    mAdapter?.NotifyItemRemoved(index);
                                }

                                GlobalContext?.ChatTab?.ArchivedChatsTab?.ShowEmptyPage();
                            }

                            var dbDatabase = new SqLiteDatabase();
                            dbDatabase.Delete_Archive(DataChatObject.ChatId);
                        }

                        switch (Type)
                        {
                            case "user":
                                {
                                    var dbDatabase = new SqLiteDatabase();

                                    var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                                    var userToDelete = mAdapter?.LastChatsList?.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                                    if (userToDelete != null)
                                    {
                                        var index = mAdapter.LastChatsList.IndexOf(userToDelete);
                                        if (index > -1)
                                        {
                                            mAdapter?.LastChatsList?.Remove(userToDelete);
                                            mAdapter?.NotifyItemRemoved(index);
                                        }

                                        GlobalContext?.ChatTab?.LastChatTab?.ShowEmptyPage();
                                    }
                                    dbDatabase.Delete_LastUsersChat(DataChatObject.UserId, "user");
                                    dbDatabase.DeleteAllMessagesUser(UserDetails.UserId, DataChatObject.UserId);

                                    Methods.Path.DeleteAll_FolderUser(DataChatObject.UserId);

                                    PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.DeleteConversationAsync(DataChatObject.UserId) });
                                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_TheConversationHasBeenDeleted), ToastLength.Long);
                                    break;
                                }
                            case "page":
                                {
                                    string userId;
                                    //remove item to my page list  
                                    var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;

                                    var checkPage = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId);
                                    if (checkPage != null)
                                    {
                                        var userAdminPage = DataChatObject.UserId;
                                        if (userAdminPage == DataChatObject.LastMessage.LastMessageClass.ToData.UserId)
                                        {
                                            userId = DataChatObject.LastMessage.LastMessageClass.UserData?.UserId;
                                            var data = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.UserData?.UserId == userId);
                                            if (data != null)
                                            {
                                                mAdapter?.LastChatsList.Remove(data);
                                                mAdapter?.NotifyItemRemoved(mAdapter.LastChatsList.IndexOf(data));

                                                GlobalContext?.ChatTab?.LastChatTab?.ShowEmptyPage();
                                            }
                                        }
                                        else
                                        {
                                            userId = DataChatObject.LastMessage.LastMessageClass.ToData.UserId;
                                            var data = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.ToData.UserId == userId);
                                            if (data != null)
                                            {
                                                mAdapter?.LastChatsList.Remove(data);
                                                mAdapter?.NotifyItemRemoved(mAdapter.LastChatsList.IndexOf(data));
                                            }
                                        }

                                        var dbDatabase = new SqLiteDatabase();
                                        dbDatabase.Delete_LastUsersChat(DataChatObject.PageId, "page", userId);

                                        Methods.Path.DeleteAll_FolderUser(DataChatObject.PageId);

                                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.PageChat.DeletePageChatAsync(DataChatObject.PageId, userId) });
                                    }


                                    ToastUtils.ShowToast(Activity, Activity.GetString(Resource.String.Lbl_TheConversationHasBeenDeleted), ToastLength.Short);

                                    break;
                                }
                            case "group":
                                {
                                    //remove item to my Group list  
                                    var adapter = GlobalContext?.ChatTab?.LastGroupChatsTab.MAdapter;
                                    var data = adapter?.LastChatsList?.FirstOrDefault(a => a.LastChat?.GroupId == DataChatObject.GroupId);
                                    if (data != null)
                                    {
                                        adapter.LastChatsList.Remove(data);
                                        adapter.NotifyItemRemoved(adapter.LastChatsList.IndexOf(data));

                                        GlobalContext?.ChatTab?.LastGroupChatsTab?.ShowEmptyPage();
                                    }

                                    var dbDatabase = new SqLiteDatabase();
                                    dbDatabase.Delete_LastUsersChat(DataChatObject.GroupId, "group");
                                    dbDatabase.DeleteAllMessagesUser(UserDetails.UserId, DataChatObject.GroupId);

                                    Methods.Path.DeleteAll_FolderUser(DataChatObject.GroupId);

                                    PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.GroupChat.DeleteGroupChatAsync(DataChatObject.GroupId) });

                                    ToastUtils.ShowToast(Activity, Activity.GetString(Resource.String.Lbl_GroupSuccessfullyLeaved), ToastLength.Short);
                                    break;
                                }
                        }

                        Dismiss();
                    }
                    catch (Exception exception)
                    {
                        Methods.DisplayReportResultTrack(exception);
                    }
                });
                dialog.SetNegativeButton(GetText(Resource.String.Lbl_No), new MaterialDialogUtils());

                dialog.Show();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        //Archive chat 
        private void ArchiveLayoutOnClick()
        {
            try
            {
                bool isArchive = false;
                Mute globalMute = null!;
                Classes.LastChatsClass checkUser = null!;

                switch (Type)
                {
                    case "user":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                            isArchive = !DataChatObject.IsArchive;
                            DataChatObject.IsArchive = isArchive;
                            globalMute = DataChatObject.Mute;

                            checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == DataChatObject.UserId);
                            if (checkUser?.LastChat != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(checkUser);
                                checkUser.LastChat.IsArchive = isArchive;

                                checkUser.LastChat.Mute.Archive = isArchive ? "yes" : "no";
                                globalMute = checkUser.LastChat.Mute;

                                mAdapter.LastChatsList.Remove(checkUser);
                                mAdapter.NotifyItemRemoved(index);

                                GlobalContext?.ChatTab?.LastChatTab?.ShowEmptyPage();
                            }
                            break;
                        }
                    case "page":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                            isArchive = !DataChatObject.IsArchive;
                            DataChatObject.IsArchive = isArchive;
                            globalMute = DataChatObject.Mute;

                            var checkPage = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId && a.LastChat?.LastMessage.LastMessageClass?.ToData?.UserId == DataChatObject.LastMessage.LastMessageClass?.ToData?.UserId);
                            if (checkPage != null)
                            {
                                var userAdminPage = DataChatObject.UserId;
                                if (userAdminPage == DataChatObject.LastMessage.LastMessageClass.ToData.UserId)
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.UserData?.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.UserData?.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.UserData?.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);
                                }
                                else
                                {
                                    var userId = DataChatObject.LastMessage.LastMessageClass.ToData.UserId;
                                    checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.LastMessage.LastMessageClass.ToData.UserId == userId);

                                    var name = DataChatObject.LastMessage.LastMessageClass.ToData.Name + "(" + DataChatObject.PageName + ")";
                                    Console.WriteLine(name);
                                }
                            }
                            else
                            {
                                checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.PageId == DataChatObject.PageId);
                            }

                            if (checkUser != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(checkUser);

                                checkUser.LastChat.IsArchive = isArchive;
                                checkUser.LastChat.Mute.Archive = isArchive ? "yes" : "no";
                                globalMute = checkUser.LastChat.Mute;

                                mAdapter.LastChatsList.Remove(checkUser);
                                mAdapter.NotifyItemRemoved(index);
                              
                                GlobalContext?.ChatTab?.LastChatTab?.ShowEmptyPage();
                            }
                            break;
                        }
                    //break;
                    case "group":
                        {
                            var mAdapter = GlobalContext?.ChatTab?.LastGroupChatsTab?.MAdapter;
                            isArchive = !DataChatObject.IsArchive;
                            DataChatObject.IsArchive = isArchive;
                            globalMute = DataChatObject.Mute;

                            checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.GroupId == DataChatObject.GroupId);
                            if (checkUser?.LastChat != null)
                            {
                                var index = mAdapter.LastChatsList.IndexOf(checkUser);
                                checkUser.LastChat.IsArchive = isArchive;

                                checkUser.LastChat.Mute.Archive = isArchive ? "yes" : "no";
                                globalMute = checkUser.LastChat.Mute;

                                mAdapter.LastChatsList.Remove(checkUser);
                                mAdapter.NotifyItemRemoved(index);
                              
                                GlobalContext?.ChatTab?.LastGroupChatsTab?.ShowEmptyPage();
                            }
                            break;
                        }
                }

                if (isArchive)
                {
                    var dictionary = new Dictionary<string, string>
                    {
                        {"archive", "yes"},
                    };

                    //if (globalMute != null)
                    //{
                    //    dictionary.Add("call_chat", globalMute.CallChat);
                    //    dictionary.Add("pin", "no");
                    //    dictionary.Add("notify", globalMute.Notify);
                    //}

                    if (Methods.CheckConnectivity())
                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.MuteChatsInfoAsync(DataChatObject.ChatId, Type, dictionary) });

                    if (checkUser != null)
                    {
                        var check = GlobalContext?.ChatTab?.ArchivedChatsTab.MAdapter.LastChatsList.FirstOrDefault(a => a.LastChat?.ChatId == DataChatObject.ChatId);
                        if (check == null)
                        {
                            GlobalContext?.ChatTab?.ArchivedChatsTab.MAdapter.LastChatsList.Add(checkUser);
                            GlobalContext?.ChatTab?.ArchivedChatsTab.MAdapter.NotifyDataSetChanged();
                          
                            GlobalContext?.ChatTab?.ArchivedChatsTab?.ShowEmptyPage();
                        }
                    }

                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_Archive), ToastLength.Long);
                }
                else
                {
                    var dictionary = new Dictionary<string, string>
                    {
                        {"archive", "no"},
                    };

                    //if (globalMute != null)
                    //{
                    //    dictionary.Add("call_chat", globalMute.CallChat);
                    //    dictionary.Add("pin", "yes");
                    //    dictionary.Add("notify", globalMute.Notify);
                    //}

                    if (Methods.CheckConnectivity())
                        PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Message.MuteChatsInfoAsync(DataChatObject.ChatId, Type, dictionary) });

                    ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_UnArchive), ToastLength.Long);

                    var check = GlobalContext?.ChatTab?.ArchivedChatsTab.MAdapter.LastChatsList.FirstOrDefault(a => a.LastChat?.ChatId == DataChatObject.ChatId);
                    if (check != null)
                    {
                        GlobalContext?.ChatTab?.ArchivedChatsTab.MAdapter.LastChatsList.Remove(check);
                        GlobalContext?.ChatTab?.ArchivedChatsTab.MAdapter.NotifyDataSetChanged();
                      
                        GlobalContext?.ChatTab?.ArchivedChatsTab?.ShowEmptyPage();
                    }
                }


                Dismiss();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }

        #endregion

        #region MaterialDialog

        public void OnInput(IDialogInterface dialog, string input)
        {
            try
            {
                switch (input.Length)
                {
                    case <= 0:
                        return;
                }

                string userId = DataChatObject.UserId;

                DataChatObject.IsReported = true;

                var mAdapter = GlobalContext?.ChatTab?.LastChatTab?.MAdapter;
                var checkUser = mAdapter?.LastChatsList.FirstOrDefault(a => a.LastChat?.UserId == userId);
                if (checkUser != null)
                {
                    var index = mAdapter.LastChatsList.IndexOf(checkUser);

                    checkUser.LastChat.IsReported = true;

                    mAdapter.NotifyItemChanged(index);
                }

                PollyController.RunRetryPolicyFunction(new List<Func<Task>> { () => RequestsAsync.Global.ReportUserAsync(userId, input) });
                ToastUtils.ShowToast(Activity, Activity.GetText(Resource.String.Lbl_HasBeenReported), ToastLength.Short);
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        #endregion

        private void LoadDataChat()
        {
            try
            {
                Page = Arguments?.GetString("Page") ?? "";
                Type = Arguments?.GetString("Type") ?? "";

                switch (Type)
                {
                    case "user":
                        {
                            DataChatObject = JsonConvert.DeserializeObject<ChatObject>(Arguments?.GetString("ItemObject") ?? "");
                            if (DataChatObject != null) //not read Change to read (Normal)
                            {
                                if (AppSettings.EnableChatArchive)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "1",
                                        Text = GetText(DataChatObject.IsArchive ? Resource.String.Lbl_UnArchive : Resource.String.Lbl_Archive),
                                        Icon = Resource.Drawable.icon_archive_vector,

                                    });
                                }

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "2",
                                    Text = GetText(Resource.String.Lbl_DeleteMessage),
                                    Icon = Resource.Drawable.icon_delete_vector,

                                });

                                if (AppSettings.EnableChatPin && Page != "Archived")
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "3",
                                        Text = GetText(DataChatObject.IsPin ? Resource.String.Lbl_UnPin : Resource.String.Lbl_Pin),
                                        Icon = Resource.Drawable.icon_pin_vector,

                                    });
                                }

                                if (AppSettings.EnableChatMute)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "4",
                                        Text = GetText(DataChatObject.IsMute ? Resource.String.Lbl_UnMuteNotification : Resource.String.Lbl_MuteNotification),
                                        Icon = Resource.Drawable.icon_mute_vector,

                                    });
                                }

                                if (AppSettings.EnableChatMakeAsRead)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "5",
                                        Text = GetText(DataChatObject.LastMessage.LastMessageClass?.Seen == "0" ? Resource.String.Lbl_MarkAsRead : Resource.String.Lbl_MarkAsUnRead),
                                        Icon = Resource.Drawable.icon_mark_chat_unread_vector,

                                    });
                                }

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "6",
                                    Text = GetText(DataChatObject.IsBlocked ? Resource.String.Btn_UnBlock : Resource.String.Lbl_Block),
                                    Icon = Resource.Drawable.icon_block_vector,

                                });

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "7",
                                    Text = GetText(Resource.String.Lbl_View_Profile),
                                    Icon = Resource.Drawable.icon_user_vector,

                                });

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "8",
                                    Text = GetText(DataChatObject.IsReported ? Resource.String.Lbl_CancelReport : Resource.String.Lbl_ReportThisUser),
                                    Icon = Resource.Drawable.icon_report_vector,
                                });
                            }

                            break;
                        }
                    case "page":
                        {
                            DataChatObject = JsonConvert.DeserializeObject<ChatObject>(Arguments?.GetString("ItemObject") ?? "");
                            if (DataChatObject != null) //not read Change to read (Normal)  
                            {
                                if (AppSettings.EnableChatArchive)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "1",
                                        Text = GetText(DataChatObject.IsArchive ? Resource.String.Lbl_UnArchive : Resource.String.Lbl_Archive),
                                        Icon = Resource.Drawable.icon_archive_vector,

                                    });
                                }

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "2",
                                    Text = GetText(Resource.String.Lbl_DeleteMessage),
                                    Icon = Resource.Drawable.icon_delete_vector,

                                });

                                if (AppSettings.EnableChatPin && Page != "Archived")
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "3",
                                        Text = GetText(DataChatObject.IsPin ? Resource.String.Lbl_UnPin : Resource.String.Lbl_Pin),
                                        Icon = Resource.Drawable.icon_pin_vector,

                                    });
                                }

                                if (AppSettings.EnableChatMute)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "4",
                                        Text = GetText(DataChatObject.IsMute ? Resource.String.Lbl_UnMuteNotification : Resource.String.Lbl_MuteNotification),
                                        Icon = Resource.Drawable.icon_mute_vector,

                                    });
                                }

                                if (AppSettings.EnableChatMakeAsRead)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "5",
                                        Text = GetText(DataChatObject.LastMessage.LastMessageClass?.Seen == "0" ? Resource.String.Lbl_MarkAsRead : Resource.String.Lbl_MarkAsUnRead),
                                        Icon = Resource.Drawable.icon_mark_chat_unread_vector,

                                    });
                                }
                            }
                            break;
                        }
                    case "group":
                        {
                            DataChatObject = JsonConvert.DeserializeObject<ChatObject>(Arguments?.GetString("ItemObject") ?? "");
                            if (DataChatObject != null) //not read Change to read (Normal)  
                            {
                                if (AppSettings.EnableChatArchive)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "1",
                                        Text = GetText(DataChatObject.IsArchive ? Resource.String.Lbl_UnArchive : Resource.String.Lbl_Archive),
                                        Icon = Resource.Drawable.icon_archive_vector,

                                    });
                                }

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "2",
                                    Text = GetText(Resource.String.Lbl_DeleteMessage),
                                    Icon = Resource.Drawable.icon_delete_vector,

                                });

                                if (AppSettings.EnableChatPin)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "3",
                                        Text = GetText(DataChatObject.IsPin ? Resource.String.Lbl_UnPin : Resource.String.Lbl_Pin),
                                        Icon = Resource.Drawable.icon_pin_vector,

                                    });
                                }

                                if (AppSettings.EnableChatMute)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "4",
                                        Text = GetText(DataChatObject.IsMute ? Resource.String.Lbl_UnMuteNotification : Resource.String.Lbl_MuteNotification),
                                        Icon = Resource.Drawable.icon_mute_vector,

                                    });
                                }

                                if (AppSettings.EnableChatMakeAsRead)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "5",
                                        Text = GetText(DataChatObject.LastMessage.LastMessageClass?.Seen == "0" ? Resource.String.Lbl_MarkAsRead : Resource.String.Lbl_MarkAsUnRead),
                                        Icon = Resource.Drawable.icon_mark_chat_unread_vector,

                                    });
                                }

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "9",
                                    Text = GetText(Resource.String.Lbl_GroupInfo),
                                    Icon = Resource.Drawable.icon_info_vector,

                                });

                                MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                {
                                    Id = "10",
                                    Text = GetText(Resource.String.Lbl_ExitGroup),
                                    Icon = Resource.Drawable.icon_logout_vector,
                                });

                                if (DataChatObject?.Owner != null && DataChatObject.Owner.Value)
                                {
                                    MAdapter.ItemOptionList.Add(new Classes.ItemOptionObject
                                    {
                                        Id = "11",
                                        Text = GetText(Resource.String.Lbl_AddMembers),
                                        Icon = Resource.Drawable.icon_user_vector,
                                    });
                                }
                            }

                            break;
                        }
                }

                MAdapter.NotifyDataSetChanged();
            }
            catch (Exception exception)
            {
                Methods.DisplayReportResultTrack(exception);
            }
        }
    }
}