<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Drop Shadow Stack -->
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#02282828" />
      <corners android:radius="8dp" />
    </shape>
  </item>
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#05282828" />
      <corners android:radius="7dp" />
    </shape>
  </item>
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#10282828" />
      <corners android:radius="6dp" />
    </shape>
  </item>
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#15282828" />
      <corners android:radius="5dp" />
    </shape>
  </item>
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#20282828" />
      <corners android:radius="4dp" />
    </shape>
  </item>
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#25282828" />
      <corners android:radius="3dp" />
    </shape>
  </item>
  <item>
    <shape>
      <padding android:top="1dp" android:right="1dp" android:bottom="1dp" android:left="1dp" />
      <solid android:color="#30282828" />
      <corners android:radius="3dp" />
    </shape>
  </item>

  <!-- Background -->
  <item>
    <shape>
      <solid android:color="#444444" />
      <corners android:radius="3dp" />
    </shape>
  </item>
</layer-list>