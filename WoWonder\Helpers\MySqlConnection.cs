using System;
using System.Data;
using MySql.Data.MySqlClient;
using WoWonder.Helpers.Utils;

namespace WoWonder.Helpers
{
    public static class MySqlConnectionHelper
    {
        private static readonly string ConnectionString = $"Server={AppSettings.HostName};" +
                                                        $"Database={AppSettings.DatabaseName};" +
                                                        $"User ID={AppSettings.DatabaseUsername};" +
                                                        $"Password={AppSettings.DatabasePassword};" +
                                                        $"Port={AppSettings.DatabasePort};" +
                                                        "SslMode=Required;";

        public static MySqlConnection GetConnection()
        {
            try
            {
                var connection = new MySqlConnection(ConnectionString);
                return connection;
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
                return null;
            }
        }

        public static bool TestConnection()
        {
            try
            {
                using var connection = GetConnection();
                if (connection == null)
                    return false;

                connection.Open();
                return connection.State == ConnectionState.Open;
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
                return false;
            }
        }
    }
}
