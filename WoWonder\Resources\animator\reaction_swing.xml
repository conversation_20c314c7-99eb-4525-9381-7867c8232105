﻿<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android">

	<objectAnimator
		android:propertyName="rotation"
		android:valueType="floatType"
		android:valueFrom="0"
		android:valueTo="2"
		android:duration="260"
		android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
		android:propertyName="rotation"
		android:valueType="floatType"
		android:valueFrom="2"
		android:valueTo="-2"
		android:startOffset="260"
		android:duration="260"
		android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
		android:propertyName="rotation"
		android:valueType="floatType"
		android:valueFrom="-2"
		android:valueTo="0"
		android:startOffset="520"
		android:duration="260"
		android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
		android:propertyName="rotation"
		android:valueType="floatType"
		android:valueFrom="0"
		android:valueTo="-2"
		android:startOffset="780"
		android:duration="260"
		android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
		android:propertyName="rotation"
		android:valueType="floatType"
		android:valueFrom="-2"
		android:valueTo="0"
		android:startOffset="1040"
		android:duration="260"
		android:interpolator="@android:interpolator/fast_out_slow_in" />

</set>