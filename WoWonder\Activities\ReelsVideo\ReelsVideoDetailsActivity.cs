using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Views;
using Android.Widget;
using AndroidX.AppCompat.App;
using AndroidX.ViewPager2.Widget;
using Bumptech.Glide;
using Bumptech.Glide.Load.Engine;
using Bumptech.Glide.Request;
using Newtonsoft.Json;
using WoWonder.Activities.ReelsVideo.Adapters;
using WoWonder.Activities.Tabbes;
using WoWonder.Helpers.Ads;
using WoWonder.Helpers.Controller;
using WoWonder.Helpers.Model;
using WoWonder.Helpers.Utils;
using WoWonder.Library.Anjo.IntegrationRecyclerView;
using WoWonder.MediaPlayers;
using WoWonderClient.Classes.Posts;
using WoWonderClient.Requests;
using Exception = System.Exception;
using Uri = Android.Net.Uri;
using Android.Views.Animations;

namespace WoWonder.Activities.ReelsVideo
{
    [Activity(Icon = "@mipmap/icon", Theme = "@style/MyTheme", ConfigurationChanges = ConfigChanges.Locale | ConfigChanges.UiMode | ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize)]
    public class ReelsVideoDetailsActivity : AppCompatActivity
    {
        #region Variables Basic

        private readonly string KeySelectedPage = "KEY_SELECTED_PAGE_REELS";

        private ViewPager2 Pager;

        private int VideosCount;
        private int SelectedPage;
        private PreCachingLayoutManager LayoutManager;
        private ReelsVideoPagerAdapter MAdapter;
        private string Type;
        private List<Classes.ReelsVideoClass> DataVideos;
        private PreCachingExoPlayerVideo PreCachingExoPlayerVideo;

        // Loading UI elements
        private RelativeLayout LoadingLayout;
        private ProgressBar ProgressBar;
        private TextView LoadingText;
        private int LoadingProgress = 0;
        private Handler ProgressHandler;

        #endregion

        #region General

        protected override void OnCreate(Bundle savedInstanceState)
        {
            try
            {
                base.OnCreate(savedInstanceState);

                Methods.App.FullScreenApp(this);

                //Overlap();

                if (savedInstanceState != null)
                {
                    SelectedPage = savedInstanceState.GetInt(KeySelectedPage);
                }

                // Create your application here
                SetContentView(Resource.Layout.ReelsVideoLayout);

                // Initialize loading UI elements
                InitLoadingUI();

                TabbedMainActivity.GetInstance()?.SetOnWakeLock();

                PreCachingExoPlayerVideo = new PreCachingExoPlayerVideo(this);

                if (Intent != null)
                {
                    Type = Intent?.GetStringExtra("Type") ?? "";
                    VideosCount = Intent?.GetIntExtra("VideosCount", 0) ?? 0;
                    if (Type == "VideoReels")
                    {
                        foreach (var video in ListUtils.VideoReelsList)
                        {
                            var check = DataVideos.FirstOrDefault(a => a.Id == video.Id);
                            if (check == null)
                            {
                                DataVideos.Add(new Classes.ReelsVideoClass
                                {
                                    Id = video.Id,
                                    Type = Classes.ItemType.ReelsVideo,
                                    VideoData = video.VideoData
                                });
                            }

                            if (AdsGoogle.NativeAdsPool?.Count > 0 && DataVideos.Count % AppSettings.ShowAdNativeReelsCount == 0)
                            {
                                DataVideos.Add(new Classes.ReelsVideoClass
                                {
                                    Type = Classes.ItemType.AdMob,
                                });
                            }
                        }
                    }
                    else
                    {
                        var list = JsonConvert.DeserializeObject<ObservableCollection<PostDataObject>>(Intent?.GetStringExtra("DataItem") ?? "");
                        if (list?.Count > 0)
                        {
                            foreach (var data in list)
                            {
                                var check = DataVideos.FirstOrDefault(a => a.Id == data.Id);
                                if (check == null)
                                {
                                    DataVideos.Add(new Classes.ReelsVideoClass
                                    {
                                        Id = data.Id,
                                        Type = Classes.ItemType.ReelsVideo,
                                        VideoData = data
                                    });
                                }

                                if (AdsGoogle.NativeAdsPool?.Count > 0 && DataVideos.Count % AppSettings.ShowAdNativeReelsCount == 0)
                                {
                                    DataVideos.Add(new Classes.ReelsVideoClass
                                    {
                                        Type = Classes.ItemType.AdMob,
                                    });
                                }
                            }
                        }

                    }
                }

                //Get Value And Set Toolbar
                InitComponent();

                if (Type == "VideoReels")
                {
                    // Show loading UI
                    ShowLoadingUI();
                    Task.Factory.StartNew(StartApiService);
                }
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        protected override void OnPause()
        {
            try
            {
                base.OnPause();
                var instance = ViewReelsVideoFragment.GetInstance();
                instance?.StopVideo();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        protected override void OnStop()
        {
            try
            {
                base.OnStop();
                var instance = ViewReelsVideoFragment.GetInstance();
                instance?.StopVideo();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        protected override void OnSaveInstanceState(Bundle outState)
        {
            try
            {
                base.OnSaveInstanceState(outState);
                outState.PutInt(KeySelectedPage, Pager.CurrentItem);
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        public override void OnTrimMemory(TrimMemory level)
        {
            try
            {
                GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced);
                base.OnTrimMemory(level);
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        public override void OnLowMemory()
        {
            try
            {
                GC.Collect(GC.MaxGeneration);
                base.OnLowMemory();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        protected override void OnDestroy()
        {
            try
            {
                TabbedMainActivity.GetInstance()?.SetOffWakeLock();
                GC.Collect(GC.MaxGeneration, GCCollectionMode.Forced);

                var instance = ViewReelsVideoFragment.GetInstance();
                instance?.StopVideo();
                PreCachingExoPlayerVideo?.Destroy();

                DestroyBasic();

                base.OnDestroy();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        #endregion

        #region Menu

        public override bool OnOptionsItemSelected(IMenuItem item)
        {
            switch (item.ItemId)
            {
                case Android.Resource.Id.Home:
                    Finish();
                    return true;
            }
            return base.OnOptionsItemSelected(item);
        }

        #endregion

        #region Functions

        private void Overlap()
        {
            try
            {
                if (Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop)
                {
                    Window?.SetFlags(WindowManagerFlags.LayoutNoLimits, WindowManagerFlags.LayoutNoLimits);
                }
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        private void InitComponent()
        {
            try
            {
                Pager = FindViewById<ViewPager2>(Resource.Id.viewpager);

                MAdapter = new ReelsVideoPagerAdapter(this, VideosCount, DataVideos);

                //Pager.CurrentItem = MAdapter.ItemCount;
                //Pager.OffscreenPageLimit = 0;

                Pager.Orientation = ViewPager2.OrientationVertical;
                //Pager.SetPageTransformer(new CustomViewPageTransformer(TransformType.Flow));
                Pager.RegisterOnPageChangeCallback(new MyOnPageChangeCallback(this));
                Pager.Adapter = MAdapter;
                Pager.Adapter.NotifyDataSetChanged();

                Pager.SetCurrentItem(SelectedPage, false);

                AdsGoogle.AdMobNative ads = new AdsGoogle.AdMobNative();
                ads.BindAdMobNative(this);
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        private void DestroyBasic()
        {
            try
            {
                Pager = null!;
                MAdapter = null!;
                SelectedPage = 0;
                
                // Clean up loading UI
                if (ProgressHandler != null)
                {
                    ProgressHandler.RemoveCallbacksAndMessages(null);
                    ProgressHandler = null;
                }
                LoadingLayout = null;
                ProgressBar = null;
                LoadingText = null;
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }
        
        private void InitLoadingUI()
        {
            try
            {
                // Try to find loading layout from the XML layout first
                LoadingLayout = FindViewById<RelativeLayout>(Resource.Id.loadingLayout);
                ProgressBar = FindViewById<ProgressBar>(Resource.Id.progressBar);
                LoadingText = FindViewById<TextView>(Resource.Id.loadingText);
                
                // If loading layout not found in XML, create it programmatically
                if (LoadingLayout == null)
                {
                    // Create loading layout programmatically
                    LoadingLayout = new RelativeLayout(this)
                    {
                        LayoutParameters = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MatchParent, ViewGroup.LayoutParams.MatchParent)
                    };
                    
                    // استخدام خلفية متدرجة بدلاً من اللون الأسود
                    LoadingLayout.SetBackgroundResource(Resource.Drawable.gradient_dark_background);
                    LoadingLayout.Alpha = 0.9f;
                    
                    // Create progress bar with video animation
                    ProgressBar = new ProgressBar(this, null, Android.Resource.Attribute.ProgressBarStyleLarge)
                    {
                        LayoutParameters = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WrapContent, ViewGroup.LayoutParams.WrapContent)
                        {
                            Width = Methods.DpToPx(this, 60),
                            Height = Methods.DpToPx(this, 60)
                        }
                    };
                    ProgressBar.Id = View.GenerateViewId();
                    ProgressBar.Indeterminate = true;
                    ProgressBar.IndeterminateDrawable = GetDrawable(Resource.Drawable.video_loading_animation);
                    ProgressBar.Progress = 0;
                    ProgressBar.Max = 100;
                    
                    // Center progress bar
                    ((RelativeLayout.LayoutParams)ProgressBar.LayoutParameters).AddRule(LayoutRules.CenterInParent);
                    
                    // Create loading text
                    LoadingText = new TextView(this)
                    {
                        LayoutParameters = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WrapContent, ViewGroup.LayoutParams.WrapContent)
                        {
                            TopMargin = Methods.DpToPx(this, 20)
                        }
                    };
                    LoadingText.Text = "جاري تحميل الفيديو";
                    LoadingText.SetTextColor(Color.White);
                    LoadingText.TextSize = 16;
                    LoadingText.Gravity = GravityFlags.Center;
                    
                    // Position text below progress bar
                    ((RelativeLayout.LayoutParams)LoadingText.LayoutParameters).AddRule(LayoutRules.Below, ProgressBar.Id);
                    ((RelativeLayout.LayoutParams)LoadingText.LayoutParameters).AddRule(LayoutRules.CenterHorizontal);
                    
                    // Add views to loading layout
                    LoadingLayout.AddView(ProgressBar);
                    LoadingLayout.AddView(LoadingText);
                    
                    // Add loading layout to the root view
                    var rootView = (ViewGroup)Window.DecorView.FindViewById(Android.Resource.Id.Content);
                    rootView.AddView(LoadingLayout);
                }
                else
                {
                    // Configure existing loading layout
                    LoadingLayout.SetBackgroundResource(Resource.Drawable.gradient_dark_background);
                    LoadingLayout.Alpha = 0.9f;
                    
                    if (ProgressBar != null)
                    {
                        ProgressBar.Indeterminate = true;
                        ProgressBar.IndeterminateDrawable = GetDrawable(Resource.Drawable.video_loading_animation);
                        ProgressBar.Progress = 0;
                        ProgressBar.Max = 100;
                    }
                    
                    if (LoadingText != null)
                    {
                        LoadingText.Text = "جاري تحميل الفيديو";
                        LoadingText.SetTextColor(Color.White);
                        LoadingText.TextSize = 16;
                    }
                }
                
                // Hide loading layout initially
                LoadingLayout.Visibility = ViewStates.Gone;
                
                // Initialize progress handler
                ProgressHandler = new Handler(Looper.MainLooper);
                
                // Log to verify initialization
                Console.WriteLine("DEBUG: InitLoadingUI completed. LoadingLayout: " + (LoadingLayout != null) + ", ProgressBar: " + (ProgressBar != null) + ", LoadingText: " + (LoadingText != null));
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }
        
        private void ShowLoadingUI()
        {
            try
            {
                if (LoadingLayout != null)
                {
                    LoadingLayout.Visibility = ViewStates.Visible;
                    LoadingProgress = 0;
                    
                    if (ProgressBar != null)
                        ProgressBar.Progress = 0;
                    
                    if (LoadingText != null)
                        LoadingText.Text = "جاري تحميل الفيديو";
                    
                    // Start progress animation
                    StartProgressAnimation();
                }
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }
        
        private void HideLoadingUI()
        {
            try
            {
                if (LoadingLayout != null)
                {
                    // Complete the progress to 100% before hiding
                    if (LoadingProgress < 100)
                    {
                        LoadingProgress = 100;
                        if (ProgressBar != null)
                            ProgressBar.Progress = 100;
                        
                        if (LoadingText != null)
                            LoadingText.Text = "تم تحميل الفيديو";
                    }
                    
                    // Create fade out animation
                    var fadeOut = new AlphaAnimation(1, 0);
                    fadeOut.Duration = 500;
                    fadeOut.AnimationEnd += (sender, args) =>
                    {
                        LoadingLayout.Visibility = ViewStates.Gone;
                    };
                    
                    // Start animation
                    LoadingLayout.StartAnimation(fadeOut);
                }
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }
        
        private void StartProgressAnimation()
        {
            try
            {
                // Simulate progress updates
                ProgressHandler?.PostDelayed(new UpdateProgressRunnable(this), 200);
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }
        
        private class UpdateProgressRunnable : Java.Lang.Object, IRunnable
        {
            private readonly WeakReference<ReelsVideoDetailsActivity> ActivityReference;
            
            public UpdateProgressRunnable(ReelsVideoDetailsActivity activity)
            {
                ActivityReference = new WeakReference<ReelsVideoDetailsActivity>(activity);
            }
            
            public void Run()
            {
                try
                {
                    if (ActivityReference.TryGetTarget(out ReelsVideoDetailsActivity activity))
                    {
                        if (activity.LoadingProgress < 100)
                        {
                            // Increment progress
                            activity.LoadingProgress += new Random().Next(5, 15);
                            if (activity.LoadingProgress > 100)
                                activity.LoadingProgress = 100;
                            
                            // Update UI
                            activity.RunOnUiThread(() =>
                            {
                                if (activity.ProgressBar != null)
                                    activity.ProgressBar.Progress = activity.LoadingProgress;
                                
                                if (activity.LoadingText != null)
                                    activity.LoadingText.Text = "جاري تحميل الفيديو";
                            });
                            
                            // Continue if not complete
                            if (activity.LoadingProgress < 100)
                                activity.ProgressHandler?.PostDelayed(this, 200);
                            else
                                activity.HideLoadingUI();
                        }
                    }
                }
                catch (Exception e)
                {
                    Methods.DisplayReportResultTrack(e);
                }
            }
        }

        #endregion

        #region Result

        protected override void OnActivityResult(int requestCode, Result resultCode, Intent data)
        {
            try
            {
                base.OnActivityResult(requestCode, resultCode, data);
                var instance = ViewReelsVideoFragment.GetInstance();
                switch (requestCode)
                {
                    case 2100 when resultCode == Result.Ok:
                        {
                            instance?.TubePlayerView?.ExitFullScreen();
                            break;
                        }
                }
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
            }
        }

        #endregion

        private class MyOnPageChangeCallback : ViewPager2.OnPageChangeCallback
        {
            private readonly ReelsVideoDetailsActivity Activity;

            public MyOnPageChangeCallback(ReelsVideoDetailsActivity activity)
            {
                try
                {
                    Activity = activity;
                }
                catch (Exception exception)
                {
                    Methods.DisplayReportResultTrack(exception);
                }
            }

            private int LastPosition = -1;
            public override void OnPageScrolled(int position, float positionOffset, int positionOffsetPixels)
            {
                try
                {
                    base.OnPageScrolled(position, positionOffset, positionOffsetPixels);

                    if (LastPosition == -1)
                    {
                        LastPosition = position;
                    }
                    else
                    {
                        LastPosition = position;
                        var instance = ViewReelsVideoFragment.GetInstance();
                        instance?.StopVideo();
                    }

                    if (position > Activity.DataVideos.Count - 3 && Activity.Type == "VideoReels")
                        Task.Factory.StartNew(Activity.StartApiService);
                }
                catch (Exception exception)
                {
                    Methods.DisplayReportResultTrack(exception);
                }
            }
        }

        private void StartApiService()
        {
            if (!Methods.CheckConnectivity())
                ToastUtils.ShowToast(this, GetString(Resource.String.Lbl_CheckYourInternetConnection), ToastLength.Short);
            else
                PollyController.RunRetryPolicyFunction(new List<Func<Task>> { GetAllPostVideo });
        }

        private bool ApiRun;
        private async Task GetAllPostVideo()
        {
            try
            {
                if (!Methods.CheckConnectivity() || ApiRun)
                    return;

                ApiRun = true;
                var postId = ListUtils.VideoReelsList.LastOrDefault()?.VideoData?.PostId ?? "0";

                var (apiStatus, respond) = await RequestsAsync.Posts.GetGlobalPost(AppSettings.PostApiLimitOnScroll, postId, "get_news_feed", "", "", "0", "0", "video");
                if (apiStatus != 200 || respond is not PostObject result || result.Data == null)
                {
                    Methods.DisplayReportResult(this, respond);
                }
                else
                {
                    bool add = false;
                    var respondList = result.Data?.Count;
                    if (respondList > 0)
                    {
                        foreach (var item in from item in result.Data let check = ListUtils.VideoReelsList.FirstOrDefault(a => a.Id == item.Id) where check == null select item)
                        {
                            var checkViewed = ListUtils.VideoReelsViewsList.FirstOrDefault(a => a.Id == item.Id);
                            if (checkViewed == null)
                            {
                                if (!AppSettings.ShowYouTubeReels && !string.IsNullOrEmpty(item.PostYoutube))
                                    continue;

                                if (!string.IsNullOrEmpty(item.PostFacebook) || !string.IsNullOrEmpty(item.PostVimeo) || !string.IsNullOrEmpty(item.PostDeepsound) || !string.IsNullOrEmpty(item.PostPlaytube))
                                    continue;

                                add = true;
                                ListUtils.VideoReelsList.Add(new Classes.ReelsVideoClass
                                {
                                    Id = item.Id,
                                    Type = Classes.ItemType.ReelsVideo,
                                    VideoData = item
                                });

                                if (AdsGoogle.NativeAdsPool?.Count > 0 && ListUtils.VideoReelsList.Count % AppSettings.ShowAdNativeReelsCount == 0)
                                {
                                    ListUtils.VideoReelsList.Add(new Classes.ReelsVideoClass
                                    {
                                        Type = Classes.ItemType.AdMob,
                                    });
                                }

                                RunOnUiThread(() =>
                                {
                                    try
                                    {
                                        Glide.With(this).Load(item.Publisher.Avatar).Apply(new RequestOptions().SetDiskCacheStrategy(DiskCacheStrategy.All).CircleCrop()).Preload();
                                    }
                                    catch (Exception e)
                                    {
                                        Methods.DisplayReportResultTrack(e);
                                    }
                                });
                            }
                        }
                    }

                    RunOnUiThread(() =>
                    {
                        try
                        {
                            if (add)
                            {
                                DataVideos = ListUtils.VideoReelsList;
                                MAdapter.UpdateReelsVideoPager(DataVideos.Count, DataVideos);
                                //Pager.Adapter.NotifyDataSetChanged();
                                
                                // Hide loading UI when data is loaded
                                HideLoadingUI();
                            }
                        }
                        catch (Exception e)
                        {
                            Methods.DisplayReportResultTrack(e);
                        }
                    });

                    ApiRun = false;
                }
            }
            catch (Exception e)
            {
                ApiRun = false;
                Methods.DisplayReportResultTrack(e);
            }
        }

    }
}