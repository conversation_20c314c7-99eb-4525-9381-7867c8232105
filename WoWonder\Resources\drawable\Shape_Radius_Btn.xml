﻿<?xml version="1.0" encoding="utf-8" ?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >

  <item
    android:bottom="0.5dp"
    android:left="0.5dp"
    android:right="0.5dp"
    android:top="0.5dp">

    <shape android:shape="rectangle" android:thickness="1dp">

      <gradient
        
        android:startColor="@color/primary"
        android:endColor="@color/primary"
        android:gradientRadius="400"
        android:type="linear" />

      <stroke android:width="0.3dp" android:color="@android:color/transparent" />

      <corners
        android:topLeftRadius="20dp"
        android:topRightRadius="20dp"
        android:bottomLeftRadius="20dp"
        android:bottomRightRadius="20dp"/>
    </shape>

  </item>

</layer-list>
