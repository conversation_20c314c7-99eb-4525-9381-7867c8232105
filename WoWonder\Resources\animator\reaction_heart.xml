﻿<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android">

	<objectAnimator
        android:propertyName="scaleX"
        android:valueType="floatType"
        android:valueFrom="1"
        android:valueTo="1.05"
        android:duration="260"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleY"
        android:valueType="floatType"
        android:valueFrom="1"
        android:valueTo="1.05"
        android:duration="260"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleX"
        android:valueType="floatType"
        android:valueFrom="1.05"
        android:valueTo="1"
        android:startOffset="260"
        android:duration="260"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleY"
        android:valueType="floatType"
        android:valueFrom="1.05"
        android:valueTo="1"
        android:startOffset="260"
        android:duration="260"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleX"
        android:valueType="floatType"
        android:valueFrom="1"
        android:valueTo="1.05"
        android:startOffset="520"
        android:duration="520"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleY"
        android:valueType="floatType"
        android:valueFrom="1"
        android:valueTo="1.05"
        android:startOffset="520"
        android:duration="520"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleX"
        android:valueType="floatType"
        android:valueFrom="1.05"
        android:valueTo="1"
        android:startOffset="1040"
        android:duration="260"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleY"
        android:valueType="floatType"
        android:valueFrom="1.05"
        android:valueTo="1"
        android:startOffset="1040"
        android:duration="260"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleX"
        android:valueType="floatType"
        android:valueFrom="1"
        android:valueTo="1.05"
        android:startOffset="1300"
        android:duration="1040"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleY"
        android:valueType="floatType"
        android:valueFrom="1"
        android:valueTo="1.05"
        android:startOffset="1300"
        android:duration="1040"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleX"
        android:valueType="floatType"
        android:valueFrom="1.05"
        android:valueTo="1"
        android:startOffset="2340"
        android:duration="520"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

	<objectAnimator
        android:propertyName="scaleY"
        android:valueType="floatType"
        android:valueFrom="1.05"
        android:valueTo="1"
        android:startOffset="2340"
        android:duration="520"
        android:interpolator="@android:interpolator/fast_out_slow_in" />

</set>
