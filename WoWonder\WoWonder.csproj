<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0-android</TargetFramework>
		<SupportedOSPlatformVersion>23</SupportedOSPlatformVersion>
		<OutputType>Exe</OutputType>
		<Nullable>disable</Nullable>
		<ImplicitUsings>disable</ImplicitUsings>
		<ApplicationId>com.seejal.soc</ApplicationId>
		<ApplicationVersion>1</ApplicationVersion>
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<Copyright>Copyright © 2025</Copyright>
		<AndroidSigningKeyStore>KeyApk\seejalsocapp.keystore</AndroidSigningKeyStore>
		<AndroidSigningKeyAlias>seejalsocapp</AndroidSigningKeyAlias>
		<AndroidSigningStorePass>Ahmed@1020304050</AndroidSigningStorePass>
		<AndroidSigningKeyPass>Ahmed@1020304050</AndroidSigningKeyPass>
		<Title>Seejal</Title>
	</PropertyGroup> 
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<JavaMaximumHeapSize>4G</JavaMaximumHeapSize>
		<AndroidKeyStore>True</AndroidKeyStore>
		<AndroidSigningKeyAlias>seejalsocapp</AndroidSigningKeyAlias>
		<AndroidSigningStorePass>Ahmed@1020304050</AndroidSigningStorePass>
		<AndroidSigningKeyPass>Ahmed@1020304050</AndroidSigningKeyPass>
		<!--apk - aab-->
		<AndroidPackageFormat>apk</AndroidPackageFormat>
		<NoWarn>1701;1702;CA1416;CA1422;CS0618;SYSLIB0039</NoWarn>
		<!--for Debug : android-arm - android-arm64 - android-x86 - android-x64 -->
		<RuntimeIdentifiers>android-arm;android-arm64;android-x86;android-x64;</RuntimeIdentifiers>
		<PublishTrimmed>False</PublishTrimmed>
		<RunAOTCompilation>False</RunAOTCompilation>
		<AndroidUseAapt2>True</AndroidUseAapt2>
		<!--<AndroidLinkTool>r8</AndroidLinkTool>-->
		<AndroidStripILAfterAOT>False</AndroidStripILAfterAOT>
		<AndroidEnableProfiledAot>False</AndroidEnableProfiledAot>
		<EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<JavaMaximumHeapSize>4G</JavaMaximumHeapSize>
		<AndroidKeyStore>True</AndroidKeyStore>
		<AndroidSigningKeyAlias>seejalsocapp</AndroidSigningKeyAlias>
		<AndroidSigningStorePass>Ahmed@1020304050</AndroidSigningStorePass>
		<AndroidSigningKeyPass>Ahmed@1020304050</AndroidSigningKeyPass>
		<!--apk - aab-->
		<AndroidPackageFormat>aab</AndroidPackageFormat>
		<!--<AndroidPackageFormats>apk;aab</AndroidPackageFormats>-->
		<NoWarn>1701;1702;CA1416;CA1422;CS0618;SYSLIB0039</NoWarn>
		<!--for Release Recommended : android-arm - android-arm64 -->
		<RuntimeIdentifiers>android-arm;android-arm64;</RuntimeIdentifiers>
		<PublishTrimmed>True</PublishTrimmed>
		<RunAOTCompilation>True</RunAOTCompilation>
		<AndroidUseAapt2>True</AndroidUseAapt2>
		<!--<AndroidLinkTool>r8</AndroidLinkTool>-->
		<AndroidStripILAfterAOT>True</AndroidStripILAfterAOT>
		<AndroidEnableProfiledAot>True</AndroidEnableProfiledAot>
		<EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
	</PropertyGroup>
	<PropertyGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)'))=='android'">
		<!--link path -->
		<AndroidManifest Condition=" Exists('$(AndroidProjectFolder)AndroidManifest.xml') ">$(AndroidProjectFolder)AndroidManifest.xml</AndroidManifest>
		<MonoAndroidResourcePrefix>$(AndroidProjectFolder)Resources</MonoAndroidResourcePrefix>
		<MonoAndroidAssetsPrefix>$(AndroidProjectFolder)Assets</MonoAndroidAssetsPrefix>
	</PropertyGroup>
	<ItemGroup>
		<Compile Remove="Payment\InitRazorPayPayment.cs" />
		<Compile Remove="Payment\Utils\PaymentBaseActivity.cs" />
		<Compile Remove="Activities\ReelsVideo\ReelsVideoDetailsActivity.cs" />
		<Compile Remove="Activities\Wallet\TabbedWalletActivity.cs" />
		<Compile Remove="Activities\ReelsVideo\ViewReelsVideoFragment.cs" />
		<Compile Remove="Helpers\Utils\FragmentBottomNavigationView.cs" />
		<Compile Remove="Payment\Utils\PaymentXBottomSheetDialog.cs" />
		<Compile Remove="Activities\ReelsVideo\ReactionReelsVideo.cs" />
		<Compile Remove="Activities\Wallet\Fragment\AddFundsFragment.cs" />
		<Compile Remove="Activities\Wallet\Fragment\SendMoneyFragment.cs" />
	</ItemGroup>
	<ItemGroup>
		<AndroidResource Include="Resources\drawable-hdpi\addImage.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\CodeVerification.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Cover_image.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Covid19.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\default_group.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\default_page.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\ic_action_pinned.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\ic_action_promote.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\image1.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\image2.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\ImagePlacholder.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\ImagePlacholder_circle.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\ImagePlacholder_circle_grey.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Image_File.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\logo.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\msg_round_load_m.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\no_profile_female_image.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\no_profile_female_image_circle.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\no_profile_image.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\no_profile_image_circle.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\splashscreen.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker1.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker10.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\sticker11.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\sticker2.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker3.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker4.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker5.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker6.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker7.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker8.webp" />
		<AndroidResource Include="Resources\drawable-hdpi\Sticker9.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\addImage.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\CodeVerification.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Cover_image.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\default_group.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\default_page.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\ic_action_pinned.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\ic_action_promote.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\image1.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\image2.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\ImagePlacholder.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\ImagePlacholder_circle.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\ImagePlacholder_circle_grey.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Image_File.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\logo.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\msg_round_load_m.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\no_profile_female_image.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\no_profile_female_image_circle.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\no_profile_image.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\no_profile_image_circle.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\splashscreen.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker1.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker10.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\sticker11.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\sticker2.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker3.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker4.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker5.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker6.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker7.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker8.webp" />
		<AndroidResource Include="Resources\drawable-xhdpi\Sticker9.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\addImage.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\CodeVerification.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Cover_image.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Covid19.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\default_group.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\default_page.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\ic_action_pinned.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\ic_action_promote.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\image1.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\image2.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\ImagePlacholder.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\ImagePlacholder_circle.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\ImagePlacholder_circle_grey.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Image_File.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\logo.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\msg_round_load_m.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\no_profile_female_image.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\no_profile_female_image_circle.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\no_profile_image.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\no_profile_image_circle.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\splashscreen.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker1.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker10.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\sticker11.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\sticker2.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker3.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker4.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker5.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker6.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker7.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker8.webp" />
		<AndroidResource Include="Resources\drawable-xxhdpi\Sticker9.webp" />
		<AndroidResource Include="Resources\drawable\addImage.webp" />
		<AndroidResource Include="Resources\drawable\category_car.webp" />
		<AndroidResource Include="Resources\drawable\category_comedy.webp" />
		<AndroidResource Include="Resources\drawable\category_economics.webp" />
		<AndroidResource Include="Resources\drawable\category_education.webp" />
		<AndroidResource Include="Resources\drawable\category_entertainment.webp" />
		<AndroidResource Include="Resources\drawable\category_gaming.webp" />
		<AndroidResource Include="Resources\drawable\category_history.webp" />
		<AndroidResource Include="Resources\drawable\category_live_style.webp" />
		<AndroidResource Include="Resources\drawable\category_movies.webp" />
		<AndroidResource Include="Resources\drawable\category_natural.webp" />
		<AndroidResource Include="Resources\drawable\category_news.webp" />
		<AndroidResource Include="Resources\drawable\category_other.webp" />
		<AndroidResource Include="Resources\drawable\category_people.webp" />
		<AndroidResource Include="Resources\drawable\category_pet.webp" />
		<AndroidResource Include="Resources\drawable\category_place.webp" />
		<AndroidResource Include="Resources\drawable\category_science.webp" />
		<AndroidResource Include="Resources\drawable\category_sport.webp" />
		<AndroidResource Include="Resources\drawable\category_travel.webp" />
		<AndroidResource Include="Resources\drawable\CodeVerification.webp" />
		<AndroidResource Include="Resources\drawable\Cover_image.webp" />
		<AndroidResource Include="Resources\drawable\Covid19.webp" />
		<AndroidResource Include="Resources\drawable\default_group.webp" />
		<AndroidResource Include="Resources\drawable\default_page.webp" />
		<AndroidResource Include="Resources\drawable\gopro_check.webp" />
		<AndroidResource Include="Resources\drawable\gopro_check_loudspeaker.webp" />
		<AndroidResource Include="Resources\drawable\gopro_eye.webp" />
		<AndroidResource Include="Resources\drawable\gopro_flag.webp" />
		<AndroidResource Include="Resources\drawable\gopro_medal.webp" />
		<AndroidResource Include="Resources\drawable\gopro_notification.webp" />
		<AndroidResource Include="Resources\drawable\ic_action_pinned.webp" />
		<AndroidResource Include="Resources\drawable\ic_action_promote.webp" />
		<AndroidResource Include="Resources\drawable\ic_copper_card.webp" />
		<AndroidResource Include="Resources\drawable\ic_plan_1.webp" />
		<AndroidResource Include="Resources\drawable\ic_plan_2.webp" />
		<AndroidResource Include="Resources\drawable\ic_plan_3.webp" />
		<AndroidResource Include="Resources\drawable\ic_plan_4.webp" />
		<AndroidResource Include="Resources\drawable\ic_post_desert.webp" />
		<AndroidResource Include="Resources\drawable\ic_post_park.webp" />
		<AndroidResource Include="Resources\drawable\ic_post_sea.webp" />
		<AndroidResource Include="Resources\drawable\image1.webp" />
		<AndroidResource Include="Resources\drawable\image2.webp" />
		<AndroidResource Include="Resources\drawable\ImagePlacholder.webp" />
		<AndroidResource Include="Resources\drawable\ImagePlacholder_circle.webp" />
		<AndroidResource Include="Resources\drawable\ImagePlacholder_circle_grey.webp" />
		<AndroidResource Include="Resources\drawable\Image_File.webp" />
		<AndroidResource Include="Resources\drawable\logo.webp" />
		<AndroidResource Include="Resources\drawable\msg_round_load_m.webp" />
		<AndroidResource Include="Resources\drawable\no_profile_female_image.webp" />
		<AndroidResource Include="Resources\drawable\no_profile_female_image_circle.webp" />
		<AndroidResource Include="Resources\drawable\no_profile_image.webp" />
		<AndroidResource Include="Resources\drawable\no_profile_image_circle.webp" />
		<AndroidResource Include="Resources\drawable\splashscreen.webp" />
		<AndroidResource Include="Resources\drawable\Sticker1.webp" />
		<AndroidResource Include="Resources\drawable\Sticker10.webp" />
		<AndroidResource Include="Resources\drawable\sticker11.webp" />
		<AndroidResource Include="Resources\drawable\sticker2.webp" />
		<AndroidResource Include="Resources\drawable\Sticker3.webp" />
		<AndroidResource Include="Resources\drawable\Sticker4.webp" />
		<AndroidResource Include="Resources\drawable\Sticker5.webp" />
		<AndroidResource Include="Resources\drawable\Sticker6.webp" />
		<AndroidResource Include="Resources\drawable\Sticker7.webp" />
		<AndroidResource Include="Resources\drawable\Sticker8.webp" />
		<AndroidResource Include="Resources\drawable\Sticker9.webp" />
		<AndroidResource Include="Resources\drawable\world_map.webp" />
	</ItemGroup>
	<ItemGroup>
		<GoogleServicesJson Include="google-services.json" />
		<ProguardConfiguration Include="Proguard.cfg" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="AndHUD" Version="2.1.0" />
		<PackageReference Include="Anjo.Android.AgoraFull" Version="4.4.1" />
		<PackageReference Include="Anjo.Android.AgoraIO.AgoraDynamicKey" Version="4.0.0" />
		<PackageReference Include="Anjo.Android.AiDeepAr" Version="5.6.16" />
		<PackageReference Include="Anjo.Android.Airbnb.Mavericks" Version="3.0.9" />
		<PackageReference Include="Anjo.Android.AppLovin" Version="13.1.0" />
		<PackageReference Include="Anjo.Android.AppLovin.Mediation.FacebookAdapter" Version="6.16.0.2" />
		<PackageReference Include="Anjo.Android.AppLovin.Mediation.GoogleAdMobAdapter" Version="23.0.0" />
		<PackageReference Include="Anjo.Android.AXEmojiView" Version="1.5.2" />
		<PackageReference Include="Anjo.Android.AXEmojiView.Ios" Version="1.5.2" />
		<PackageReference Include="Anjo.Android.BrainTree.BrowserSwitch" Version="2.7.0.1" />
		<PackageReference Include="Anjo.Android.BrainTree.Card" Version="4.49.1" />
		<PackageReference Include="Anjo.Android.BrainTree.CardForm" Version="5.4.0.7" />
		<PackageReference Include="Anjo.Android.BrainTree.Core" Version="4.49.1" />
		<PackageReference Include="Anjo.Android.BrainTree.DataCollector" Version="4.49.1" />
		<PackageReference Include="Anjo.Android.BrainTree.PayPal" Version="4.49.1" />
		<PackageReference Include="Anjo.Android.BrainTree.PayPalDataCollector" Version="4.49.1" />
		<PackageReference Include="Anjo.Android.Facebook" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Facebook.Applinks" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Facebook.AudienceNetwork" Version="6.19.0" />
		<PackageReference Include="Anjo.Android.Facebook.Common" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Facebook.Core" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Facebook.Login" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Facebook.Messenger" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Facebook.Share" Version="18.0.2" />
		<PackageReference Include="Anjo.Android.Cashfree.Analytics" Version="1.0.19" />
		<PackageReference Include="Anjo.Android.Cashfree.Api" Version="2.1.27" />
		<PackageReference Include="Anjo.Android.Cashfree.Base" Version="1.0.4.1" />
		<PackageReference Include="Anjo.Android.Cashfree.Core" Version="2.1.27" />
		<PackageReference Include="Anjo.Android.Cashfree.ImageCaching" Version="1.0.8.1" />
		<PackageReference Include="Anjo.Android.Cashfree.Network" Version="1.0.6.1" />
		<PackageReference Include="Anjo.Android.Cashfree.Ui" Version="2.1.27" />
		<PackageReference Include="Anjo.Android.Flutterwave" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Cache" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Commons" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Core" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Logger" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Presentation" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Remote" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.Flutterwave.Utils" Version="2.1.39.3" />
		<PackageReference Include="Anjo.Android.GoogleAccompanist.ThemeAdapterAppcompat" Version="0.34.0" />
		<PackageReference Include="Anjo.Android.GoogleAccompanist.ThemeAdapterCore" Version="0.34.0" />
		<PackageReference Include="Anjo.Android.GoogleAccompanist.ThemeAdapterMaterial" Version="0.34.0" />
		<PackageReference Include="Anjo.Android.GoogleAccompanist.ThemeAdapterMaterial3" Version="0.34.0" />
		<PackageReference Include="Anjo.Android.GooglePayButton.ComposePayButton" Version="1.1.0" />
		<PackageReference Include="Anjo.Android.GoogleAds.Mediation.AppLovinAdapter" Version="12.1.0.1" />
		<PackageReference Include="Anjo.Android.GoogleServices.Ads" Version="24.0.0" />
		<PackageReference Include="Anjo.Android.GoogleServices.AdsBase" Version="24.0.0" />
		<PackageReference Include="Anjo.Android.GoogleServices.AdsLite" Version="24.0.0" />
		<PackageReference Include="Anjo.Android.HtmlTextView" Version="4.0.0" />
		<PackageReference Include="Anjo.Android.LeonidsLib" Version="1.3.2.2" />
		<PackageReference Include="Anjo.Android.Lottie" Version="6.6.3" />
		<PackageReference Include="Anjo.Android.Media3.Common" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.Container" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.Database" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.DataSource" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.Decoder" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.ExoPlayer" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.ExoPlayer.Dash" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.ExoPlayer.Hls" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.ExoPlayer.Rtsp" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.ExoPlayer.SmoothStreaming" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.Extractor" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.Session" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.Media3.Ui" Version="1.5.1" />
		<PackageReference Include="Anjo.Android.OneSignal" Version="5.1.29" />
		<PackageReference Include="Anjo.Android.OneSignal.Core" Version="5.1.29" />
		<PackageReference Include="Anjo.Android.OneSignal.InAppMessages" Version="5.1.29" />
		<PackageReference Include="Anjo.Android.OneSignal.Location" Version="5.1.29" />
		<PackageReference Include="Anjo.Android.OneSignal.Notifications" Version="5.1.29" />
		<PackageReference Include="Anjo.Android.PhotoEditor" Version="3.0.2" />
		<PackageReference Include="Anjo.Android.PixImagePicker" Version="*******" />
		<PackageReference Include="Anjo.Android.Razorpay" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.3DSecue" Version="*******" />
		<PackageReference Include="Anjo.Android.Stripe.Attestation" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.Core" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.Hcaptcha" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.PaymentCore" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.PaymentModel" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.PaymentSheet" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.PaymentsUiCore" Version="********" />
		<PackageReference Include="Anjo.Android.Stripe.UiCore" Version="********" />
		<PackageReference Include="Anjo.Android.YouTubePlayerX" Version="*******" />
		<PackageReference Include="Anjo.Android.Twilio.Video" Version="7.8.0" />
		<PackageReference Include="Anjoxam.BillingClient.Droid" Version="7.1.1" />
		<PackageReference Include="Anjo.Google.Android.InstantApps" Version="*******" />
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Crc32.NET" Version="1.2.0" />
		<PackageReference Include="DrawableToolbox" Version="1.0.7" />
		<PackageReference Include="FlexboxLayout.Xamarin.Android" Version="3.0.0" />
		<PackageReference Include="GoogleGson" Version="********" />
		<PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
		<PackageReference Include="Karamunting.Android.AmulyaKhare.TextDrawable" Version="*******" />
		<PackageReference Include="Karamunting.Android.JaredRummler.ColorPicker" Version="1.1.0" />
		<PackageReference Include="Karamunting.Android.HDodenhof.CircleImageView" Version="3.1.0" />
		<PackageReference Include="Karamunting.Android.Ongakuer.CircleIndicator" Version="2.1.4" />
		<PackageReference Include="Karamunting.Android.Sephiroth74.ImageViewZoom" Version="2.3.0" />
		<PackageReference Include="Karamunting.Android.IGreenWood.SimpleCropView" Version="1.1.8" />
		<PackageReference Include="Karamunting.Android.TeamSupercharge.ShimmerLayout" Version="2.1.0" />
		<PackageReference Include="Laerdal.FFmpeg.Android.Min.Gpl" Version="4.4.28" />
		<PackageReference Include="MySql.Data" Version="9.3.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Plugin.Fingerprint" Version="3.0.0-beta.1" />
		<PackageReference Include="Polly" Version="8.5.2" />
		<PackageReference Include="sqlite-net-pcl" Version="1.9.172" />
		<PackageReference Include="SQLitePCLRaw.bundle_green" Version="2.1.11" />
		<PackageReference Include="SQLitePCLRaw.core" Version="2.1.11" />
		<PackageReference Include="SQLitePCLRaw.lib.e_sqlite3.android" Version="2.1.11" />
		<PackageReference Include="SQLitePCLRaw.provider.e_sqlite3" Version="2.1.11" />
		<PackageReference Include="Square.OkHttp3" Version="4.12.0.9" />
		<PackageReference Include="Square.OkHttp3.LoggingInterceptor" Version="4.12.0.9" />
		<PackageReference Include="Square.Retrofit2" Version="2.11.0.5" />
		<PackageReference Include="Square.Retrofit2.ConverterGson" Version="2.11.0.5" />
		<PackageReference Include="Square.Retrofit2.ConverterScalars" Version="2.11.0.5" />
		<PackageReference Include="Xam.Plugin.Geolocator" Version="5.0.0-beta" />
		<PackageReference Include="Xam.Plugins.AndroidX.SlidingUpPanel" Version="1.0.0" />
		<PackageReference Include="Xamarin.Android.Glide" Version="4.16.0.12" />
		<PackageReference Include="Xamarin.Android.Glide.Annotations" Version="4.16.0.12" />
		<PackageReference Include="Xamarin.Android.Glide.GifDecoder" Version="4.16.0.12" />
		<PackageReference Include="Xamarin.Android.Glide.RecyclerViewIntegration" Version="4.16.0.12" />
		<PackageReference Include="Xamarin.Android.JetBrains.Kotlin_Parcelize_Runtime" Version="1.5.20.1" />
		<PackageReference Include="Xamarin.Android.ReactiveX.RxAndroid" Version="2.1.1.18" />
		<PackageReference Include="Xamarin.Android.ReactiveX.RxJava" Version="2.2.21.25" />
		<PackageReference Include="Xamarin.Android.RoundedImageView" Version="2.3.0" />
		<PackageReference Include="Xamarin.AndroidX.Activity" Version="1.10.1.1" />
		<PackageReference Include="Xamarin.AndroidX.Activity.Compose" Version="1.10.1.1" />
		<PackageReference Include="Xamarin.AndroidX.Annotation" Version="1.9.1.3" />
		<PackageReference Include="Xamarin.AndroidX.AppCompat" Version="1.7.0.6" />
		<PackageReference Include="Xamarin.AndroidX.AppCompat.AppCompatResources" Version="1.7.0.6" />
		<PackageReference Include="Xamarin.AndroidX.Arch.Core.Common" Version="2.2.0.16" />
		<PackageReference Include="Xamarin.AndroidX.Arch.Core.Runtime" Version="2.2.0.16" />
		<PackageReference Include="Xamarin.AndroidX.AsyncLayoutInflater" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.Browser" Version="1.8.0.9" />
		<PackageReference Include="Xamarin.AndroidX.Camera.Camera2" Version="1.4.2.1" />
		<PackageReference Include="Xamarin.AndroidX.Camera.Core" Version="1.4.2.1" />
		<PackageReference Include="Xamarin.AndroidX.Camera.Lifecycle" Version="1.4.2.1" />
		<PackageReference Include="Xamarin.AndroidX.Camera.View" Version="1.4.2.1" />
		<PackageReference Include="Xamarin.AndroidX.CardView" Version="1.0.0.34" />
		<PackageReference Include="Xamarin.AndroidX.Collection" Version="1.5.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Collection.Ktx" Version="1.5.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.Foundation" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.Material" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.Material.Icons.Core" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.Runtime" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.UI" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.UI.Tooling.Preview" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.Compose.UI.ViewBinding" Version="1.7.8.1" />
		<PackageReference Include="Xamarin.AndroidX.ConstraintLayout" Version="2.2.1.1" />
		<PackageReference Include="Xamarin.AndroidX.ConstraintLayout.Solver" Version="2.0.4.27" />
		<PackageReference Include="Xamarin.AndroidX.CoordinatorLayout" Version="1.3.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Core" Version="1.16.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Core.SplashScreen" Version="*******5" />
		<PackageReference Include="Xamarin.AndroidX.Credentials" Version="1.5.0.1" />
		<PackageReference Include="Xamarin.AndroidX.Credentials.PlayServicesAuth" Version="1.5.0.1" />
		<PackageReference Include="Xamarin.AndroidX.CursorAdapter" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.CustomView" Version="1.1.0.31" />
		<PackageReference Include="Xamarin.AndroidX.DataBinding.DataBindingAdapters" Version="8.9.1.1" />
		<PackageReference Include="Xamarin.AndroidX.DataBinding.DataBindingCommon" Version="8.9.1.1" />
		<PackageReference Include="Xamarin.AndroidX.DataBinding.DataBindingRuntime" Version="8.9.1.1" />
		<PackageReference Include="Xamarin.AndroidX.DataBinding.ViewBinding" Version="8.9.1.1" />
		<PackageReference Include="Xamarin.AndroidX.DocumentFile" Version="1.0.1.32" />
		<PackageReference Include="Xamarin.AndroidX.DrawerLayout" Version="1.2.0.16" />
		<PackageReference Include="Xamarin.AndroidX.DynamicAnimation" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.ExifInterface" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.Fragment" Version="1.8.6.1" />
		<PackageReference Include="Xamarin.AndroidX.Fragment.Ktx" Version="1.8.6.1" />
		<PackageReference Include="Xamarin.AndroidX.GridLayout" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.Interpolator" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Leanback" Version="1.0.0.34" />
		<PackageReference Include="Xamarin.AndroidX.Legacy.Support.Core.UI" Version="1.0.0.33" />
		<PackageReference Include="Xamarin.AndroidX.Legacy.Support.Core.Utils" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Legacy.Support.V13" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Legacy.Support.V4" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Common" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Common.Jvm" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Extensions" Version="2.2.0.32" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.LiveData.Core" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Process" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Runtime" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Runtime.Ktx" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.Service" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.ViewModel" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.ViewModel.Android" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.ViewModel.Compose" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.ViewModel.Compose.Android" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.ViewModel.Ktx" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Lifecycle.ViewModelSavedState" Version="2.8.7.3" />
		<PackageReference Include="Xamarin.AndroidX.Loader" Version="1.1.0.32" />
		<PackageReference Include="Xamarin.AndroidX.LocalBroadcastManager" Version="*******0" />
		<PackageReference Include="Xamarin.AndroidX.Media" Version="1.7.0.10" />
		<PackageReference Include="Xamarin.AndroidX.Media2.Common" Version="1.3.0.10" />
		<PackageReference Include="Xamarin.AndroidX.Migration" Version="1.0.10" />
		<PackageReference Include="Xamarin.AndroidX.MultiDex" Version="2.0.1.32" />
		<PackageReference Include="Xamarin.AndroidX.Palette" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Preference" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Print" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.RecyclerView" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.SavedState" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Security.SecurityCrypto" Version="*******-alpha06" />
		<PackageReference Include="Xamarin.AndroidX.SlidingPaneLayout" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.SwipeRefreshLayout" Version="*******7" />
		<PackageReference Include="Xamarin.AndroidX.Transition" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.VectorDrawable" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.VectorDrawable.Animated" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.VersionedParcelable" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.ViewPager" Version="*******" />
		<PackageReference Include="Xamarin.AndroidX.Work.Runtime" Version="********" />
		<PackageReference Include="Xamarin.AndroidX.Work.Work.Runtime.Ktx" Version="********" />
		<PackageReference Include="Xamarin.BadgeView" Version="1.1.3" />
		<PackageReference Include="Xamarin.Build.Download" Version="0.11.4" />
		<PackageReference Include="Xamarin.Essentials" Version="1.8.1" />
		<PackageReference Include="Xamarin.Facebook.Shimmer" Version="1.0.4" />
		<PackageReference Include="Xamarin.Firebase.Common" Version="*********" />
		<PackageReference Include="Xamarin.Firebase.Messaging" Version="124.1.1.1" />
		<PackageReference Include="Xamarin.Google.Accompanist.FlowLayout" Version="0.36.0.4" />
		<PackageReference Include="Xamarin.Google.Android.DataTransport.TransportRuntime" Version="4.0.0.3" />
		<PackageReference Include="Xamarin.Google.Android.Material" Version="1.12.0.3" />
		<PackageReference Include="Xamarin.Google.Android.Places" Version="2.4.0" />
		<PackageReference Include="Xamarin.Google.Android.Play.Integrity" Version="1.4.0.4" />
		<PackageReference Include="Xamarin.Google.AutoValue.Annotations" Version="1.11.0.6" />
		<PackageReference Include="Xamarin.Google.Dagger" Version="2.56.2.1" />
		<PackageReference Include="Xamarin.Google.Guava" Version="33.4.8.1" />
		<PackageReference Include="Xamarin.Google.Guava.FailureAccess" Version="1.0.3.1" />
		<PackageReference Include="Xamarin.Google.UserMessagingPlatform" Version="3.2.0.1" />
		<PackageReference Include="Xamarin.Google.ZXing.Core" Version="3.5.3.7" />
		<PackageReference Include="Xamarin.GoogleAndroid.Libraries.Identity.GoogleId" Version="1.1.0.9" />
		<PackageReference Include="Xamarin.GooglePlayServices.Ads.Identifier" Version="118.2.0.2" />
		<PackageReference Include="Xamarin.GooglePlayServices.AppSet" Version="116.1.0.5" />
		<PackageReference Include="Xamarin.GooglePlayServices.Auth" Version="121.3.0.2" />
		<PackageReference Include="Xamarin.GooglePlayServices.Auth.Api.Phone" Version="118.2.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Auth.Base" Version="118.1.0.2" />
		<PackageReference Include="Xamarin.GooglePlayServices.Base" Version="118.7.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Basement" Version="118.7.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Cast" Version="122.0.0.3" />
		<PackageReference Include="Xamarin.GooglePlayServices.Cast.Framework" Version="122.0.0.3" />
		<PackageReference Include="Xamarin.GooglePlayServices.Clearcut" Version="117.0.0.21" />
		<PackageReference Include="Xamarin.GooglePlayServices.CloudMessaging" Version="117.3.0.5" />
		<PackageReference Include="Xamarin.GooglePlayServices.Gcm" Version="117.0.0.21" />
		<PackageReference Include="Xamarin.GooglePlayServices.Iid" Version="117.0.0.21" />
		<PackageReference Include="Xamarin.GooglePlayServices.Location" Version="121.3.0.5" />
		<PackageReference Include="Xamarin.GooglePlayServices.Maps" Version="119.2.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Measurement" Version="122.4.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Measurement.Api" Version="122.4.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Measurement.Base" Version="122.4.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Measurement.Sdk.Api" Version="122.4.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Nearby" Version="119.3.0.5" />
		<PackageReference Include="Xamarin.GooglePlayServices.Phenotype" Version="117.0.0.21" />
		<PackageReference Include="Xamarin.GooglePlayServices.Places.PlaceReport" Version="117.1.0.5" />
		<PackageReference Include="Xamarin.GooglePlayServices.Stats" Version="117.1.0.5" />
		<PackageReference Include="Xamarin.GooglePlayServices.Tasks" Version="118.3.0.1" />
		<PackageReference Include="Xamarin.GooglePlayServices.Wallet" Version="119.4.0.5" />
		<PackageReference Include="Xamarin.Jakewharton.DiskLruCache" Version="2.0.2.6" />
		<PackageReference Include="Xamarin.Kotlin.StdLib" Version="2.0.21.3" />
		<PackageReference Include="Xamarin.Kotlin.StdLib.Jdk7" Version="2.0.21.3" />
		<PackageReference Include="Xamarin.KotlinX.Coroutines.Android" Version="1.9.0.3" />
		<PackageReference Include="Xamarin.KotlinX.Coroutines.Core" Version="1.9.0.3" />
		<PackageReference Include="Xamarin.KotlinX.Coroutines.Play.Services" Version="1.9.0.3" />
		<PackageReference Include="Xamarin.KotlinX.Serialization.Core.Jvm" Version="*******" />
		<PackageReference Include="Xamarin.ReLinker" Version="1.4.4" />
	</ItemGroup>
	<ItemGroup>
		<Reference Include="AamarPay">
			<HintPath>..\API\AamarPay.dll</HintPath>
		</Reference>
		<Reference Include="CircleButton">
			<HintPath>..\API\CircleButton.dll</HintPath>
		</Reference>
		<Reference Include="InAppBilling">
		  <HintPath>..\API\InAppBilling.dll</HintPath>
		</Reference>
		<Reference Include="IyziPay">
			<HintPath>..\API\IyziPay.dll</HintPath>
		</Reference>
		<Reference Include="SecurionPay">
			<HintPath>..\API\SecurionPay.dll</HintPath>
		</Reference>
		<Reference Include="SocketIOClient">
			<HintPath>..\API\SocketIOClient.dll</HintPath>
		</Reference>
		<Reference Include="TextDecorator">
			<HintPath>..\API\TextDecorator.dll</HintPath>
		</Reference>
		<Reference Include="WoWonderClient">
		  <HintPath>..\API\WoWonderClient.dll</HintPath>
		</Reference>
	</ItemGroup>
</Project>
