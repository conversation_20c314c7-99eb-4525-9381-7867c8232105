using System;
using System.Collections.Generic;
using System.Data;
using MySql.Data.MySqlClient;
using WoWonder.Helpers.Utils;

namespace WoWonder.Helpers
{
    public static class MySqlHelper
    {
        public static DataTable ExecuteQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using var connection = MySqlConnectionHelper.GetConnection();
                if (connection == null)
                    return null;

                connection.Open();

                using var command = new MySqlCommand(query, connection);
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value);
                    }
                }

                var dataTable = new DataTable();
                using var adapter = new MySqlDataAdapter(command);
                adapter.Fill(dataTable);

                return dataTable;
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
                return null;
            }
        }

        public static int ExecuteNonQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using var connection = MySqlConnectionHelper.GetConnection();
                if (connection == null)
                    return -1;

                connection.Open();

                using var command = new MySqlCommand(query, connection);
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value);
                    }
                }

                return command.ExecuteNonQuery();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
                return -1;
            }
        }

        public static object ExecuteScalar(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using var connection = MySqlConnectionHelper.GetConnection();
                if (connection == null)
                    return null;

                connection.Open();

                using var command = new MySqlCommand(query, connection);
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value);
                    }
                }

                return command.ExecuteScalar();
            }
            catch (Exception e)
            {
                Methods.DisplayReportResultTrack(e);
                return null;
            }
        }
    }
}
